from apify_client import ApifyClient
from typing import List, Dict, Union, Optional
from dataclasses import dataclass
from enum import Enum
import json
from datetime import datetime
import logging
from collections import defaultdict

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comment_scraper.log'),
        logging.StreamHandler()
    ]
)

class ScrapeStatus(Enum):
    SUCCESS = "success"
    FAILED = "failed"
    PENDING = "pending"

@dataclass
class ScrapeResult:
    url: str
    status: ScrapeStatus
    error_message: Optional[str] = None
    comments: Optional[List['CommentData']] = None
    timestamp: str = datetime.now().isoformat()

@dataclass
class PlatformStats:
    total_urls: int = 0
    successful_urls: int = 0
    failed_urls: int = 0
    total_comments: int = 0
    error_messages: List[str] = None

    def __post_init__(self):
        if self.error_messages is None:
            self.error_messages = []

    def to_dict(self) -> Dict:
        return {
            "total_urls": self.total_urls,
            "successful_urls": self.successful_urls,
            "failed_urls": self.failed_urls,
            "success_rate": f"{(self.successful_urls / self.total_urls * 100):.2f}%" if self.total_urls > 0 else "0%",
            "total_comments": self.total_comments,
            "error_messages": self.error_messages
        }

class SocialMediaPlatform(Enum):
    INSTAGRAM = "instagram"
    FACEBOOK = "facebook"
    LINKEDIN = "linkedin"
    TIKTOK = "tiktok"

@dataclass
class CommentData:
    text: str
    author_name: str
    author_url: Optional[str] = None
    author_title: Optional[str] = None
    likes_count: int = 0
    replies_count: int = 0
    timestamp: Optional[str] = None
    comment_id: Optional[str] = None
    is_verified: bool = False
    parent_comment_id: Optional[str] = None

class CommentScraper:
    def __init__(self, api_token: str):
        """
        Initialize the CommentScraper with API token
        
        Args:
            api_token: Apify API token
        """
        self.api_token = api_token
        self.client = ApifyClient(api_token)
        self.actor_ids = {
            SocialMediaPlatform.INSTAGRAM: "SbK00X0JYCPblD2wp",
            SocialMediaPlatform.FACEBOOK: "us5srxAYnsrkgUv2v",
            SocialMediaPlatform.LINKEDIN: "8xICdc1375BbZdgE3",
            SocialMediaPlatform.TIKTOK: "BDec00yAmCm1QbMEI"
        }
        self.stats = {platform: PlatformStats() for platform in SocialMediaPlatform}
        self.results = defaultdict(dict)

    def _log_scrape_result(self, platform: SocialMediaPlatform, result: ScrapeResult):
        """Log the result of a scrape attempt"""
        if result.status == ScrapeStatus.SUCCESS:
            logging.info(f"Successfully scraped {platform.value} post: {result.url}")
            self.stats[platform].successful_urls += 1
            self.stats[platform].total_comments += len(result.comments) if result.comments else 0
        else:
            logging.error(f"Failed to scrape {platform.value} post: {result.url} - Error: {result.error_message}")
            self.stats[platform].failed_urls += 1
            self.stats[platform].error_messages.append(f"{result.url}: {result.error_message}")
        
        self.stats[platform].total_urls += 1
        self.results[platform][result.url] = result

    def _scrape_instagram(self, urls: List[str], limit: int = 10) -> Dict[str, List[CommentData]]:
        """Scrape Instagram comments"""
        results = {}
        for url in urls:
            try:
                run_input = {
                    "directUrls": [url],
                    "resultsLimit": limit
                }
                run = self.client.actor(self.actor_ids[SocialMediaPlatform.INSTAGRAM]).call(run_input=run_input)
                comments = []
                for item in self.client.dataset(run["defaultDatasetId"]).iterate_items():
                    comments.append(CommentData(
                        text=item.get("text", ""),
                        author_name=item.get("ownerUsername", ""),
                        author_url=item.get("ownerProfilePicUrl", ""),
                        likes_count=item.get("likesCount", 0),
                        timestamp=item.get("timestamp", "")
                    ))
                results[url] = comments
                self._log_scrape_result(
                    SocialMediaPlatform.INSTAGRAM,
                    ScrapeResult(url=url, status=ScrapeStatus.SUCCESS, comments=comments)
                )
            except Exception as e:
                self._log_scrape_result(
                    SocialMediaPlatform.INSTAGRAM,
                    ScrapeResult(url=url, status=ScrapeStatus.FAILED, error_message=str(e))
                )
        return results

    def _scrape_facebook(self, urls: List[str], limit: int = 50, include_nested: bool = False) -> Dict[str, List[CommentData]]:
        """Scrape Facebook comments"""
        results = {}
        for url in urls:
            try:
                run_input = {
                    "startUrls": [{"url": url}],
                    "resultsLimit": limit,
                    "includeNestedComments": include_nested,
                    "viewOption": "RANKED_UNFILTERED"
                }
                run = self.client.actor(self.actor_ids[SocialMediaPlatform.FACEBOOK]).call(run_input=run_input)
                comments = []
                for item in self.client.dataset(run["defaultDatasetId"]).iterate_items():
                    comments.append(CommentData(
                        text=item.get("text", ""),
                        author_name=item.get("authorName", ""),
                        author_url=item.get("authorUrl", ""),
                        likes_count=item.get("likesCount", 0),
                        replies_count=item.get("repliesCount", 0),
                        timestamp=item.get("timestamp", ""),
                        comment_id=item.get("commentId", ""),
                        is_verified=item.get("isVerified", False),
                        parent_comment_id=item.get("parentCommentId", "")
                    ))
                results[url] = comments
                self._log_scrape_result(
                    SocialMediaPlatform.FACEBOOK,
                    ScrapeResult(url=url, status=ScrapeStatus.SUCCESS, comments=comments)
                )
            except Exception as e:
                self._log_scrape_result(
                    SocialMediaPlatform.FACEBOOK,
                    ScrapeResult(url=url, status=ScrapeStatus.FAILED, error_message=str(e))
                )
        return results

    def _scrape_linkedin(self, urls: List[str], sort_type: str = "RELEVANCE") -> Dict[str, List[CommentData]]:
        """Scrape LinkedIn comments"""
        results = {}
        for url in urls:
            try:
                run_input = {
                    "postUrl": url,
                    "sortType": sort_type,
                    "startPage": 1,
                    "minDelay": 2,
                    "maxDelay": 7,
                    "proxy": {
                        "useApifyProxy": True,
                        "apifyProxyCountry": "US",
                    }
                }
                run = self.client.actor(self.actor_ids[SocialMediaPlatform.LINKEDIN]).call(run_input=run_input)
                comments = []
                for item in self.client.dataset(run["defaultDatasetId"]).iterate_items():
                    comments.append(CommentData(
                        text=item.get("text", ""),
                        author_name=item.get("authorName", ""),
                        author_title=item.get("authorTitle", ""),
                        author_url=item.get("authorProfileUrl", ""),
                        likes_count=item.get("likesCount", 0),
                        replies_count=item.get("repliesCount", 0),
                        timestamp=item.get("timestamp", ""),
                        comment_id=item.get("commentId", "")
                    ))
                results[url] = comments
                self._log_scrape_result(
                    SocialMediaPlatform.LINKEDIN,
                    ScrapeResult(url=url, status=ScrapeStatus.SUCCESS, comments=comments)
                )
            except Exception as e:
                self._log_scrape_result(
                    SocialMediaPlatform.LINKEDIN,
                    ScrapeResult(url=url, status=ScrapeStatus.FAILED, error_message=str(e))
                )
        return results

    def _scrape_tiktok(self, urls: List[str], comments_per_post: int = 100) -> Dict[str, List[CommentData]]:
        """Scrape TikTok comments"""
        results = {}
        for url in urls:
            try:
                run_input = {
                    "postURLs": [url],
                    "commentsPerPost": comments_per_post,
                    "maxRepliesPerComment": 2,
                    "resultsPerPage": 100,
                    "profileScrapeSections": ["videos"],
                    "profileSorting": "latest",
                    "excludePinnedPosts": False,
                }
                run = self.client.actor(self.actor_ids[SocialMediaPlatform.TIKTOK]).call(run_input=run_input)
                comments = []
                for item in self.client.dataset(run["defaultDatasetId"]).iterate_items():
                    comments.append(CommentData(
                        text=item.get("text", ""),
                        author_name=item.get("uniqueId", ""),
                        author_url=item.get("avatarThumbnail", ""),
                        likes_count=item.get("diggCount", 0),
                        timestamp=item.get("createTimeISO", ""),
                        replies_count=item.get("replyCommentTotal", 0)
                    ))
                results[url] = comments
                self._log_scrape_result(
                    SocialMediaPlatform.TIKTOK,
                    ScrapeResult(url=url, status=ScrapeStatus.SUCCESS, comments=comments)
                )
            except Exception as e:
                self._log_scrape_result(
                    SocialMediaPlatform.TIKTOK,
                    ScrapeResult(url=url, status=ScrapeStatus.FAILED, error_message=str(e))
                )
        return results

    def scrape_comments(self, 
                       platform: SocialMediaPlatform, 
                       urls: List[str], 
                       **kwargs) -> Dict[str, List[CommentData]]:
        """
        Scrape comments from specified social media platform
        
        Args:
            platform: Social media platform to scrape from
            urls: List of post URLs to scrape
            **kwargs: Additional platform-specific parameters
            
        Returns:
            Dict mapping URLs to lists of CommentData objects
        """
        try:
            if platform == SocialMediaPlatform.INSTAGRAM:
                return self._scrape_instagram(urls, **kwargs)
            elif platform == SocialMediaPlatform.FACEBOOK:
                return self._scrape_facebook(urls, **kwargs)
            elif platform == SocialMediaPlatform.LINKEDIN:
                return self._scrape_linkedin(urls, **kwargs)
            elif platform == SocialMediaPlatform.TIKTOK:
                return self._scrape_tiktok(urls, **kwargs)
            else:
                raise ValueError(f"Unsupported platform: {platform}")
        except Exception as e:
            raise Exception(f"Error scraping {platform.value} comments: {str(e)}")

    def get_platform_stats(self) -> Dict[str, Dict]:
        """Get statistics for all platforms"""
        return {
            platform.value: stats.to_dict()
            for platform, stats in self.stats.items()
        }

    def generate_report(self) -> str:
        """Generate a detailed report of scraping results"""
        report = ["=== Comment Scraping Report ===\n"]
        
        # Overall statistics
        total_urls = sum(stats.total_urls for stats in self.stats.values())
        total_successful = sum(stats.successful_urls for stats in self.stats.values())
        total_failed = sum(stats.failed_urls for stats in self.stats.values())
        total_comments = sum(stats.total_comments for stats in self.stats.values())
        
        report.append(f"Overall Statistics:")
        report.append(f"Total URLs processed: {total_urls}")
        report.append(f"Successful scrapes: {total_successful}")
        report.append(f"Failed scrapes: {total_failed}")
        report.append(f"Total comments collected: {total_comments}")
        report.append(f"Overall success rate: {(total_successful / total_urls * 100):.2f}%\n")
        
        # Platform-specific statistics
        for platform, stats in self.stats.items():
            report.append(f"\n{platform.value.upper()} Statistics:")
            report.append(f"Total URLs: {stats.total_urls}")
            report.append(f"Successful: {stats.successful_urls}")
            report.append(f"Failed: {stats.failed_urls}")
            report.append(f"Success rate: {(stats.successful_urls / stats.total_urls * 100):.2f}%" if stats.total_urls > 0 else "0%")
            report.append(f"Total comments: {stats.total_comments}")
            
            if stats.error_messages:
                report.append("\nError Messages:")
                for error in stats.error_messages:
                    report.append(f"- {error}")
        
        return "\n".join(report)

def format_comments_for_output(comments: Dict[str, List[CommentData]]) -> Dict:
    """Format comments into a structured output format"""
    return {
        url: [
            {
                "text": comment.text,
                "author": {
                    "name": comment.author_name,
                    "url": comment.author_url,
                    "title": comment.author_title,
                    "is_verified": comment.is_verified
                },
                "engagement": {
                    "likes": comment.likes_count,
                    "replies": comment.replies_count
                },
                "metadata": {
                    "timestamp": comment.timestamp,
                    "comment_id": comment.comment_id,
                    "parent_comment_id": comment.parent_comment_id
                }
            }
            for comment in comment_list
        ]
        for url, comment_list in comments.items()
    }

# Example usage
if __name__ == "__main__":
    API_TOKEN = "**********************************************"
    
    # Initialize scraper
    scraper = CommentScraper(API_TOKEN)
    
    # Example URLs for each platform
    urls = {
        SocialMediaPlatform.INSTAGRAM: [
            "https://www.instagram.com/p/DJtyFcRM-Xn/"
        ],
        SocialMediaPlatform.FACEBOOK: [
            "https://www.facebook.com/humansofnewyork/posts/pfbid0BbKbkisExKGSKuhee9a7i86RwRuMKFC8NSkKStB7CsM3uXJuAAfZLrkcJMXxhH4Yl"
        ],
        SocialMediaPlatform.LINKEDIN: [
            "https://www.linkedin.com/posts/linkedin_add-job-preferences-activity-6704397478923423745-OR6o"
        ],
        SocialMediaPlatform.TIKTOK: [
            "https://www.tiktok.com/@bellapoarch/video/6862153058223197445"
        ]
    }
    
    # Scrape comments from all platforms
    all_results = {}
    for platform, platform_urls in urls.items():
        try:
            comments = scraper.scrape_comments(platform, platform_urls)
            formatted_comments = format_comments_for_output(comments)
            all_results[platform.value] = formatted_comments
        except Exception as e:
            logging.error(f"Error scraping {platform.value}: {str(e)}")
    
    # Generate and print report
    report = scraper.generate_report()
    print("\n" + report)
    
    # Save results to file
    with open('scraping_results.json', 'w') as f:
        json.dump(all_results, f, indent=2)
    
    # Save report to file
    with open('scraping_report.txt', 'w') as f:
        f.write(report) 