# Group Scraping for Telegram and WhatsApp

This document explains the new group-based scraping functionality for Telegram and WhatsApp platforms, which requires manual group joining before member scraping can begin.

## Overview

Unlike other social media platforms (Instagram, Facebook, LinkedIn, TikTok) that can be scraped directly via APIs, Telegram and WhatsApp require users to manually join groups before member information can be collected. This implementation provides a two-phase approach:

1. **Phase 1**: Find relevant groups and notify users to join them
2. **Phase 2**: After manual joining, scrape member information from all groups with retry mechanism

## Workflow

### 1. Job Creation
When a user creates a scraping job for Telegram or WhatsApp:

```bash
POST /api/scraping/create-job
{
    "user_id": "user123",
    "business_domain": "Technology",
    "business_goals": "Lead generation for software development",
    "target_market": "Small to medium businesses",
    "business_website_url": "https://example.com",
    "target_platforms": ["telegram"],
    "leads_per_platform": {"telegram": 50},
    "additional_criteria": "Interested in software development"
}
```

### 2. Group Discovery
The system automatically:
- Searches for relevant groups using the business criteria
- Stores group links in the database
- Sets job status to `awaiting_manual_action`
- Sends email notification with group previews

### 3. Manual Group Joining
Users receive an email with group links and must manually:
- Join the relevant groups on Telegram/WhatsApp
- Note which groups they successfully joined

### 4. Trigger Member Scraping (Simplified)
After joining groups, users call the manual action completion endpoint with just the parent job ID and platform:

```bash
POST /api/scraping/manual-action-completed
{
    "parent_job_id": "job_abc123",
    "platform": "telegram"
}
```

### 5. Member Scraping with Retry
The system automatically:
- Finds all group links for the specified parent job and platform
- Attempts to scrape members from all groups
- Uses retry mechanism (3 retries with exponential backoff)
- Handles successful and failed group scraping separately
- Stores member data in the database
- Updates job status to `completed` or `failed`

## API Endpoints

### Get Pending Groups by Parent Job
```bash
GET /api/scraping/pending-groups/{parent_job_id}/{platform}
```

Returns groups that need manual joining for the specified parent job and platform.

**Response:**
```json
{
    "parent_job_id": "job_abc123",
    "platform": "telegram",
    "pending_groups": [
        {
            "child_job_id": "job_def456",
            "group_name": "Tech Developers",
            "group_link": "https://t.me/techdevelopers",
            "member_count": 1500,
            "join_status": "pending"
        }
    ],
    "total_groups": 1
}
```

### Complete Manual Action (Simplified)
```bash
POST /api/scraping/manual-action-completed
```

Marks all groups as joined and triggers member scraping with retry mechanism.

**Request:**
```json
{
    "parent_job_id": "job_abc123",
    "platform": "telegram"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Manual action completed. Started scraping 5 groups for telegram with retry mechanism.",
    "parent_job_id": "job_abc123",
    "platform": "telegram",
    "groups_found": 5,
    "child_job_ids": ["job_def456", "job_ghi789"]
}
```

### Get All Scraped Users
```bash
GET /api/scraping/users/{parent_job_id}
```

Returns all scraped users for a parent job, organized by platform with comprehensive summary.

**Response:**
```json
{
    "parent_job_id": "job_abc123",
    "total_users": 150,
    "platforms": [
        {
            "platform": "telegram",
            "total_users": 85,
            "users": [
                {
                    "user_id": "123456789",
                    "username": "john_doe",
                    "display_name": "John Doe",
                    "bio": "Software Developer",
                    "profile_url": "https://t.me/john_doe",
                    "profile_image_url": "https://example.com/avatar.jpg",
                    "location": "New York",
                    "verified": false,
                    "follower_count": 0,
                    "following_count": 0,
                    "additional_data": {
                        "group_name": "Tech Developers",
                        "first_name": "John",
                        "last_name": "Doe"
                    },
                    "scraped_at": "2024-01-15T10:30:00Z"
                }
            ]
        },
        {
            "platform": "instagram",
            "total_users": 65,
            "users": [...]
        }
    ],
    "summary": {
        "telegram": {
            "total_users": 85,
            "completed_jobs": 1,
            "failed_jobs": 0,
            "pending_jobs": 0
        },
        "instagram": {
            "total_users": 65,
            "completed_jobs": 1,
            "failed_jobs": 0,
            "pending_jobs": 0
        },
        "overall": {
            "total_users": 150,
            "total_platforms": 2,
            "parent_job_status": "completed",
            "parent_job_created": "2024-01-15T09:00:00Z",
            "parent_job_completed": "2024-01-15T11:00:00Z"
        }
    }
}
```

### Get Scraped Users by Platform
```bash
GET /api/scraping/users/{parent_job_id}/{platform}
```

Returns scraped users for a specific platform within a parent job.

**Response:**
```json
{
    "parent_job_id": "job_abc123",
    "platform": "telegram",
    "total_users": 85,
    "users": [
        {
            "user_id": "123456789",
            "username": "john_doe",
            "display_name": "John Doe",
            "bio": "Software Developer",
            "profile_url": "https://t.me/john_doe",
            "profile_image_url": "https://example.com/avatar.jpg",
            "location": "New York",
            "verified": false,
            "follower_count": 0,
            "following_count": 0,
            "additional_data": {
                "group_name": "Tech Developers",
                "first_name": "John",
                "last_name": "Doe"
            },
            "scraped_at": "2024-01-15T10:30:00Z",
            "child_job_id": "job_def456"
        }
    ],
    "child_jobs": [
        {
            "job_id": "job_def456",
            "status": "completed",
            "leads_requested": 50,
            "leads_scraped": 85,
            "started_at": "2024-01-15T09:30:00Z",
            "completed_at": "2024-01-15T10:30:00Z"
        }
    ]
}
```

## Key Features

### Simplified User Experience
- Users only need to provide `parent_job_id` and `platform`
- No need to manually specify which groups were joined
- System automatically finds all group links for the job

### Retry Mechanism
- Built-in retry mechanism with exponential backoff
- 3 retry attempts with increasing delays (1s, 2s, 4s)
- Handles temporary connection issues and rate limiting

### Robust Error Handling
- Individual group failure doesn't stop the entire process
- Tracks successful vs failed groups separately
- Detailed logging for troubleshooting

### Status Tracking
- Group status: `pending` → `joined` → `scraped`/`failed`
- Job status: `awaiting_manual_action` → `scraping_members` → `completed`/`failed`
- Comprehensive error messages and logging

## Configuration

### Telegram Configuration
Add these environment variables to your `.env` file:

```env
TELEGRAM_API_ID=27928447
TELEGRAM_API_HASH=f9ab963bd50e9669e6ea7d7da4da1e0f
TELEGRAM_PHONE=+2349017491405
```

### WhatsApp Configuration
Add this environment variable to your `.env` file:

```env
WHATSAPP_TOKEN=IvvTzJXb9hPkunTFBOhptCi8WRDceQnU
```

## Database Schema

### GroupLink Table
Stores information about groups that need manual joining:

```sql
CREATE TABLE group_links (
    id SERIAL PRIMARY KEY,
    child_job_id VARCHAR(50) REFERENCES child_jobs(job_id),
    platform VARCHAR(20),
    group_name VARCHAR(255),
    group_link VARCHAR(500),
    group_id VARCHAR(100),
    member_count INTEGER,
    join_status VARCHAR(50) DEFAULT 'pending',
    manual_join_required BOOLEAN DEFAULT TRUE,
    scraped_at TIMESTAMP DEFAULT NOW()
);
```

### Job Status Flow
1. `scheduled` → Initial job creation
2. `in_progress` → Job processing started
3. `awaiting_manual_action` → Groups found, waiting for user to join
4. `scraping_members` → Manual action completed, scraping members
5. `completed` → Member scraping finished
6. `failed` → Error occurred during processing

### Group Status Flow
1. `pending` → Group discovered, waiting for joining
2. `joined` → User has joined the group
3. `scraped` → Successfully scraped members from group
4. `failed` → Failed to scrape members from group

## Testing

Use the provided test script to verify the functionality:

```bash
python test_group_scraping_flow.py
```

This script demonstrates the complete simplified flow:
1. Creating a scraping job
2. Checking pending groups
3. Completing manual action (simplified)
4. Monitoring job status with retry mechanism

## Error Handling

### Common Issues

1. **No groups found**: The system couldn't find relevant groups for the business criteria
2. **Connection failed**: Telegram/WhatsApp credentials are invalid or connection issues
3. **Permission denied**: The bot doesn't have permission to access group members
4. **Rate limiting**: Too many requests to the platform APIs
5. **Partial failures**: Some groups succeed while others fail

### Troubleshooting

1. **Check credentials**: Verify Telegram/WhatsApp API credentials are correct
2. **Verify group links**: Ensure group links are valid and accessible
3. **Check job logs**: Review the `job_logs` table for detailed error messages
4. **Monitor status**: Use the job status endpoint to track progress
5. **Retry mechanism**: The system automatically retries failed attempts

## Security Considerations

1. **API Credentials**: Store sensitive credentials in environment variables
2. **Rate Limiting**: Implement appropriate delays between API calls
3. **Data Privacy**: Ensure compliance with platform terms of service
4. **Access Control**: Validate user permissions before allowing group scraping
5. **Error Logging**: Avoid logging sensitive information in error messages

## Future Enhancements

1. **User-specific jobs**: Match jobs to specific users instead of processing the most recent
2. **Batch processing**: Allow processing multiple groups simultaneously
3. **Progress tracking**: Real-time updates on scraping progress
4. **Webhook notifications**: Real-time notifications when scraping completes
5. **Advanced retry strategies**: Configurable retry policies per platform
6. **Group filtering**: Allow users to specify which groups to skip 