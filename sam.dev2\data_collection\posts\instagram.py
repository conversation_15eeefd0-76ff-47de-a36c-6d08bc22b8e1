import requests
import re
import os
import sys
from typing import Dict, List
from dotenv import load_dotenv

from .base_collector import BaseSocialMediaCollector
from config.settings import Config
class InstagramCollector(BaseSocialMediaCollector):
    def __init__(self):
        self.api_key = Config.api_keys.INSTAGRAM_RAPID_API_KEY
        # print(f"API Key: {self.api_key}")
        self.api_host = "instagram-scrapper-posts-reels-stories-downloader.p.rapidapi.com"
        self.headers = {
            'x-rapidapi-key': self.api_key,
            'x-rapidapi-host': self.api_host
        }
        # For post details (optional, keep your old endpoint)
        self.post_details_api_host = "instagram-scraper-stable-api.p.rapidapi.com"
        self.post_details_headers = {
            'x-rapidapi-key': self.api_key,
            'x-rapidapi-host': self.post_details_api_host
        }

    def extract_username_from_url(self, url: str) -> str:
        """Extract username from Instagram profile URL."""
        match = re.search(r'instagram\.com/([^/?#]+)/?', url)
        if match:
            return match.group(1)
        return url  # If not a URL, assume it's already a username

    def get_user_id(self, username_or_url: str) -> str:
        """Get the user ID (pk) from a username or profile URL."""
        username = username_or_url
        if "instagram.com" in username_or_url:
            username = self.extract_username_from_url(username_or_url)
        url = f"https://{self.api_host}/profile_by_username"
        params = {"username": username}
        resp = requests.get(url, headers=self.headers, params=params, timeout=15)
        if resp.status_code == 200:
            data = resp.json()
            user_id = data.get("pk") or data.get("pk_id") or data.get("strong_id__")
            if user_id:
                return str(user_id)
        print(f"Could not resolve user ID for username: {username_or_url}")
        return None

    def get_profile_posts(self, url: str, count: int = 20) -> List[str]:
        """
        Get all post URLs for a profile using user_id.
        Args:
            url: Instagram URL or username
            count: Number of posts to retrieve (default: 20)
        Returns:
            List of Instagram post URLs
        """
        try:
            user_id = self.get_user_id(url)
            if not user_id:
                return []
            api_url = f"https://{self.api_host}/posts_by_user_id"
            params = {"user_id": user_id}
            post_urls = []
            next_max_id = None

            while len(post_urls) < count:
                if next_max_id:
                    params["next_max_id"] = next_max_id
                resp = requests.get(api_url, headers=self.headers, params=params, timeout=15)
                if resp.status_code != 200:
                    print(f"Failed to fetch posts for user_id {user_id}: {resp.text}")
                    break
                data = resp.json()
                items = data.get("items") or data.get("data") or data.get("posts") or []
                for post in items:
                    if isinstance(post, dict):
                        shortcode = post.get("code") or post.get("shortcode") or post.get("id")
                        if shortcode:
                            post_url = f"https://www.instagram.com/p/{shortcode}/"
                            post_urls.append(post_url)
                            if len(post_urls) >= count:
                                break
                # Pagination
                next_max_id = data.get("next_max_id")
                if not next_max_id or not items:
                    break
            return post_urls[:count]
        except Exception as e:
            print(f"Error in get_profile_posts: {e}")
            return []

    def get_company_posts(self, url: str, count: int = 20) -> List[str]:
        """
        Get posts for a company (same as profile posts for Instagram)
        Args:
            url: Instagram company URL or username
            count: Number of posts to retrieve (default: 20)
        Returns:
            List of Instagram post URLs
        """
        return self.get_profile_posts(url, count)

    def extract_shortcode_from_url(self, url: str) -> str:
        """Extract shortcode from Instagram URL"""
        shortcode_pattern = r'instagram\.com/p/([A-Za-z0-9_-]+)'
        shortcode_match = re.search(shortcode_pattern, url)
        if shortcode_match:
            return shortcode_match.group(1)
        raise ValueError(f"Could not extract shortcode from URL: {url}")

    def get_post_details(self, post_url: str) -> Dict:
        """Get post details - focusing only on returning the post URL"""
        try:
            if not post_url.startswith(('http://', 'https://')):
                post_url = f"https://www.instagram.com/p/{post_url}/"
            url = f"https://{self.post_details_api_host}/get_media_data.php"
            querystring = {
                "reel_post_code_or_url": post_url,
                "type": "post"
            }
            response = requests.get(
                url,
                headers=self.post_details_headers,
                params=querystring,
                timeout=15
            )
            shortcode = self.extract_shortcode_from_url(post_url) if post_url.startswith(('http://', 'https://')) else post_url
            if response.status_code == 200:
                return {
                    "shortcode": shortcode,
                    "post_url": post_url
                }
            return {
                "shortcode": shortcode,
                "post_url": post_url
            }
        except Exception:
            return {}

# Example usage
if __name__ == "__main__":
    collector = InstagramCollector()

    # Example: Get posts for a user
    profile_url = "https://www.instagram.com/instagram/"
    count = 10
    print(f"Getting {count} post URLs for Instagram user: {profile_url}")
    profile_posts = collector.get_profile_posts(profile_url, count)
    if profile_posts:
        print(f"\nFound {len(profile_posts)} posts for {profile_url}")
        for i, post_url in enumerate(profile_posts, 1):
            print(f"{i}. {post_url}")
    else:
        print(f"No posts found for {profile_url} or an error occurred.")

    # Example: Get posts for a company (same as user)
    company_url = "instagram"
    print(f"\nGetting {count} post URLs for Instagram company: {company_url}")
    company_posts = collector.get_company_posts(company_url, count)
    if company_posts:
        print(f"\nFound {len(company_posts)} posts for {company_url}")
        for i, post_url in enumerate(company_posts, 1):
            print(f"{i}. {post_url}")
    else:
        print(f"No posts found for {company_url} or an error occurred.")


    # Test get_post_details
    test_post_url = "https://www.instagram.com/p/DLDbXx9StFr/"  # Replace with any valid post URL
    print(f"\nGetting details for Instagram post: {test_post_url}")
    details = collector.get_post_details(test_post_url)
    if details:
        print("Post details:")
        print(details)
    else:
        print("No details found or an error occurred.")