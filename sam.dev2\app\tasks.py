import dramatiq
from dramatiq.brokers.redis import Redis<PERSON>roker
from sqlalchemy.orm import Session
from datetime import datetime
import json
from .models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ob, JobLog, GroupLink, ScrapedUser
from .database import get_db_session, test_db_connection
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging
import sys
from pathlib import Path
import csv
import asyncio
from config.settings import Config
from src.components.data_collection.posts.social_comment_scraper import (
    InstagramCollector, TikTokCollector, FacebookCollector, LinkedInCollector,
    SocialMediaPlatform, extract_unique_profiles, collect_and_scrape
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configure Dramatiq broker with Redis using config
redis_broker = RedisBroker(
    host=Config.redis.REDIS_HOST,
    port=Config.redis.REDIS_PORT,
    db=Config.redis.REDIS_DB
)
dramatiq.set_broker(redis_broker)

# Test database connection on module load
if not test_db_connection():
    logger.error("Failed to connect to database. Please check your database configuration.")
    raise Exception("Database connection failed")

@dramatiq.actor(
    queue_name="parent_job_tasks",
    priority=0,
    time_limit=3600000,
    max_retries=0,
    actor_name="process_parent_job"
)
def process_parent_job(parent_job_id: str):
    """Process a parent job by creating and initiating child jobs."""
    db = get_db_session()
    try:
        parent_job = db.query(ParentJob).filter(ParentJob.job_id == parent_job_id).first()
        if not parent_job:
            return

        # Update parent job status
        parent_job.status = 'pending'
        db.commit()

        # Fetch all child jobs for this parent
        child_jobs = db.query(ChildJob).filter(ChildJob.parent_job_id == parent_job_id).all()
        for child_job in child_jobs:
            # Start child job processing
            process_child_job.send(child_job.job_id)

        # Log the job creation
        log = JobLog(
            job_id=parent_job_id,
            job_type='parent',
            log_level='info',
            message='Parent job processing started'
        )
        db.add(log)
        db.commit()

    except Exception as e:
        log = JobLog(
            job_id=parent_job_id,
            job_type='parent',
            log_level='error',
            message=f'Error processing parent job: {str(e)}'
        )
        db.add(log)
        db.commit()
    finally:
        db.close()

@dramatiq.actor(
    queue_name="child_job_tasks",
    priority=0,
    time_limit=3600000,
    max_retries=0,
    actor_name="process_child_job"
)
def process_child_job(child_job_id: str):
    """Process a child job for a specific platform."""
    db = get_db_session()
    try:
        child_job = db.query(ChildJob).filter(ChildJob.job_id == child_job_id).first()
        if not child_job:
            return

        # Update child job status
        child_job.status = 'in_progress'
        child_job.started_at = datetime.now()
        db.commit()

        # Initialize appropriate handler based on platform
        if child_job.platform in ['telegram', 'whatsapp']:
            process_group_based_platform.send(child_job_id)
        else:
            process_direct_platform.send(child_job_id)

    except Exception as e:
        log = JobLog(
            job_id=child_job_id,
            job_type='child',
            log_level='error',
            message=f'Error processing child job: {str(e)}'
        )
        db.add(log)
        db.commit()
    finally:
        db.close()


@dramatiq.actor(
    queue_name="group_based_platform_tasks",
    priority=0,
    time_limit=3600000,
    max_retries=0,
    actor_name="process_group_based_platform"
)
def process_group_based_platform(child_job_id: str):
    """Process platforms that require group joining (Telegram/WhatsApp)."""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger("process_group_based_platform")
    logger.info(f"process_group_based_platform called with child_job_id={child_job_id}")
    db = get_db_session()
    try:
        child_job = db.query(ChildJob).filter(ChildJob.job_id == child_job_id).first()
        if not child_job:
            return

      

        from data_collection.web_search.social_links_extractor import SocialMediaPipeline
        
        pipeline = SocialMediaPipeline(
            tavily_api_key=Config.api_keys.TAVILY_API_KEY,
            openai_api_key=Config.api_keys.OPENAI_API_KEY
        )
        
        # Extract company info from scraping_criteria
        # The scraping_criteria should contain website_url and company_goals
        scraping_criteria = child_job.scraping_criteria or {}
        if isinstance(scraping_criteria, str):
            try:
                scraping_criteria = json.loads(scraping_criteria)
            except Exception as e:
                logger.error(f"Failed to parse scraping_criteria JSON: {e}")
                scraping_criteria = {}
        logger.info(f"scraping_criteria: {scraping_criteria}")
        company_url = scraping_criteria.get('website_url', '')
        company_goals = scraping_criteria.get('business_goals', '')
        logger.info(f"company_url: {company_url}, company_goals: {company_goals}")
        
        try:
            # Run pipeline with company URL
            url_results = pipeline.run_pipeline(
                company_url=company_url,
                company_goals=None
            ) if company_url else {"telegram_groups": [], "whatsapp_groups": [], "twitter_links": []}

            # Run pipeline with company goals
            goals_results = pipeline.run_pipeline(
                company_url=None,
                company_goals=company_goals
            ) if company_goals else {"telegram_groups": [], "whatsapp_groups": [], "twitter_links": []}

            # Combine results and remove duplicates by URL
            combined_results = {
                "telegram_groups": [],
                "whatsapp_groups": [],
                "twitter_links": []
            }
            for media_type in combined_results.keys():
                seen_urls = set()
                for result in url_results.get(media_type, []) + goals_results.get(media_type, []):
                    if result["url"] not in seen_urls:
                        seen_urls.add(result["url"])
                        combined_results[media_type].append(result)

            # Save combined results as JSON
            with open(f'pipeline_results_{child_job_id}.json', 'w', encoding='utf-8') as f:
                json.dump(combined_results, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved combined pipeline results to pipeline_results_{child_job_id}.json")

            # Use the relevant group list for the current platform
            if child_job.platform == 'telegram':
                groups = combined_results.get('telegram_groups', [])
            else:
                groups = combined_results.get('whatsapp_groups', [])
        except Exception as e:
            logger.error(f"Exception in run_pipeline: {str(e)}")
            raise

        # Store group links in DB
        for group in groups:
            group_link = GroupLink(
                child_job_id=child_job_id,
                platform=child_job.platform,
                group_name=group.get('title', ''),
                group_link=group.get('url', ''),
                member_count=group.get('member_count', 0)
            )
            db.add(group_link)
        db.commit()

        # Export groups to CSV
        csv_file_path = f"group_links_{child_job_id}.csv"
        # Add platform info to each group for CSV
        for group in groups:
            group['platform'] = child_job.platform
        # export_groups_to_csv(groups, csv_file_path)

        # Update job status
        child_job.status = 'awaiting_manual_action'
        db.commit()

        # Send email notification with CSV
        send_manual_action_email(child_job_id, csv_file_path)

    except Exception as e:
        pass
        # log = JobLog(
        #     job_id=child_job_id,
        #     job_type='child',
        #     log_level='error',
        #     message=f'Error processing group-based platform: {str(e)}'
        # )
        # db.add(log)
        # db.commit()
        logger.error(f"Exception in process_group_based_platform: {str(e)}")
    finally:
        db.close()

@dramatiq.actor(
    queue_name="direct_platform_tasks",
    priority=0,
    time_limit=3600000,
    max_retries=0,
    actor_name="process_direct_platform"
)
def process_direct_platform(child_job_id: str):
    """Process platforms with direct API access (Facebook, Instagram, LinkedIn, TikTok).
    Uses the competitor discovery pipeline to get competitor URLs, then scrapes those profiles for leads as before.
    """
    db = get_db_session()
    try:
        child_job = db.query(ChildJob).filter(ChildJob.job_id == child_job_id).first()
        if not child_job:
            return

        # Extract business info from scraping_criteria
        scraping_criteria = child_job.scraping_criteria or {}
        if isinstance(scraping_criteria, str):
            try:
                scraping_criteria = json.loads(scraping_criteria)
            except Exception:
                scraping_criteria = {}
        business_name = scraping_criteria.get('business_domain')
        business_url = scraping_criteria.get('website_url')
        products_services = scraping_criteria.get('business_goals')

        # Use the competitor discovery pipeline (as in test.py)
        from src.components.data_collection.web_search.similar_company_finder import SimilarCompanyFinder
        from src.components.data_collection.posts.social_comment_scraper import (
            InstagramCollector, TikTokCollector, FacebookCollector, LinkedInCollector,
            SocialMediaPlatform, extract_unique_profiles, collect_and_scrape
        )

        finder = SimilarCompanyFinder()
        competitors = finder.find_competitors(
            company_name=business_name,
            company_url=business_url,
            products_services=products_services
        )
        platform_key = child_job.platform.lower()
        platform_map = {
            'instagram': (InstagramCollector, SocialMediaPlatform.INSTAGRAM),
            'facebook': (FacebookCollector, SocialMediaPlatform.FACEBOOK),
            'linkedin': (LinkedInCollector, SocialMediaPlatform.LINKEDIN),
            'tiktok': (TikTokCollector, SocialMediaPlatform.TIKTOK),
        }
        if platform_key not in platform_map:
            logger.error(f"Unsupported platform for direct scraping: {child_job.platform}")
            return
        CollectorClass, platform_enum = platform_map[platform_key]
        all_leads = []
        for comp in competitors:
            socials = finder.find_company_socials(comp['name'], comp['url'])
            urls = socials.get(platform_key, [])
            # Only use the first URL for this platform to avoid redundancy
            if urls:
                url = urls[0]
                collector = CollectorClass()
                post_count = child_job.leads_requested or 5
                formatted_comments = collect_and_scrape(platform_enum, collector, url, post_count=post_count)
                unique_profiles = extract_unique_profiles(platform_key, formatted_comments)
                # Store unique user profiles in DB (as before)
                for profile in unique_profiles.values():
                    scraped_user = ScrapedUser(
                        child_job_id=child_job_id,
                        platform=child_job.platform,
                        user_id=profile.get('name'),
                        username=profile.get('name'),
                        display_name=profile.get('name'),
                        bio=profile.get('title'),
                        follower_count=None,
                        following_count=None,
                        profile_url=profile.get('profile_url'),
                        profile_image_url=profile.get('url'),
                        location=None,
                        verified=profile.get('is_verified', False),
                        additional_data=profile
                    )
                    db.add(scraped_user)
                all_leads.extend(unique_profiles.values())
        db.commit()
        child_job.status = 'completed'
        child_job.completed_at = datetime.now()
        child_job.leads_scraped = len(all_leads)
        db.commit()
        check_parent_job_completion.send(child_job.parent_job_id)
    except Exception as e:
        log = JobLog(
            job_id=child_job_id,
            job_type='child',
            log_level='error',
            message=f'Error processing direct platform: {str(e)}'
        )
        db.add(log)
        db.commit()
    finally:
        db.close()


@dramatiq.actor(
    queue_name="parent_job_completion_tasks",
    priority=0,
    time_limit=3600000,
    max_retries=0,
    actor_name="check_parent_job_completion"
)
def check_parent_job_completion(parent_job_id: str):
    """Check if all child jobs are completed and update parent job status."""
    db = get_db_session()
    try:
        parent_job = db.query(ParentJob).filter(ParentJob.job_id == parent_job_id).first()
        if not parent_job:
            return

        # Check all child jobs
        child_jobs = db.query(ChildJob).filter(ChildJob.parent_job_id == parent_job_id).all()
        all_completed = all(job.status == 'completed' for job in child_jobs)
        
        if all_completed:
            parent_job.status = 'completed'
            parent_job.actual_completion_time = datetime.now()
            parent_job.total_leads_scraped = sum(job.leads_scraped for job in child_jobs)
            db.commit()

            # Send completion notification
            send_completion_email(parent_job_id)

    except Exception as e:
        log = JobLog(
            job_id=parent_job_id,
            job_type='parent',
            log_level='error',
            message=f'Error checking parent job completion: {str(e)}'
        )
        db.add(log)
        db.commit()
    finally:
        db.close()

def send_manual_action_email(child_job_id: str, csv_file_path: str = None):
    """Send email notification for manual group joining, listing group previews instead of attaching a CSV."""
    db = get_db_session()
    try:
        child_job = db.query(ChildJob).filter(ChildJob.job_id == child_job_id).first()
        if not child_job:
            return

        parent_job = db.query(ParentJob).filter(ParentJob.job_id == child_job.parent_job_id).first()
        
        # Fetch all group links for this child job
        groups = db.query(GroupLink).filter(GroupLink.child_job_id == child_job_id).all()
        
        # Separate by platform for preview
        telegram_groups = [g for g in groups if g.platform == 'telegram']
        whatsapp_groups = [g for g in groups if g.platform == 'whatsapp']

        # Prepare preview (up to 10 for each)
        def group_preview(group_list):
            preview = ""
            for g in group_list[:10]:
                preview += f"- {g.group_name or '(No Name)'}: {g.group_link} (Members: {g.member_count})\n"
            return preview or "(None found)\n"

        # Create email content
        msg = MIMEMultipart()
        msg['Subject'] = f'Manual Action Required - Group Joining Preview'
        msg['From'] = Config.email.SMTP_FROM
        msg['To'] = Config.email.ADMIN_EMAIL
        

        body = f"""
        Dear Admin,

        Parent Job ID: {parent_job.job_id}
        Business Domain: {parent_job.business_domain}

        There are {len(telegram_groups)} Telegram groups and {len(whatsapp_groups)} WhatsApp groups that require manual joining for this job.

        --- Telegram Groups Preview (up to 10) ---\n{group_preview(telegram_groups)}
        --- WhatsApp Groups Preview (up to 10) ---\n{group_preview(whatsapp_groups)}

        Please join these groups and then update the job status to \"manual_action_completed\" in the admin panel.

        Current Status: Awaiting Manual Action
        Next Steps: Join groups → Update status → Automatic member scraping will resume
        """

        msg.attach(MIMEText(body, 'plain'))

        # Send email
        #print(f"SENDING EMAIL: {Config.email.SMTP_HOST}, {Config.email.SMTP_PORT}, {Config.email.SMTP_USER}, {Config.email.SMTP_PASSWORD}")
        print(f"SENDING EMAIL....")
        print("Connecting to SMTP...")
        with smtplib.SMTP(Config.email.SMTP_HOST, Config.email.SMTP_PORT, timeout=10) as server:
            print("Starting TLS...")
            server.starttls()
            print("Logging in...")
            server.login(Config.email.SMTP_USER, Config.email.SMTP_PASSWORD)
            print("Sending message...")
            server.send_message(msg)
            print("Email sent!")

    except Exception as e:
        print(f"ERROR IN SENDING EMAIL: {e}")
    finally:
        db.close()


def send_completion_email(parent_job_id: str):
    """Send email notification for job completion."""
    db = get_db_session()
    try:
        parent_job = db.query(ParentJob).filter(ParentJob.job_id == parent_job_id).first()
        if not parent_job:
            return

        # Create email content
        msg = MIMEMultipart()
        msg['Subject'] = 'Scraping Job Completed'
        msg['From'] = Config.email.SMTP_FROM
        msg['To'] = Config.email.ADMIN_EMAIL

        body = f"""
        Dear Admin,

        The scraping job has been completed successfully.

        Job Details:
        - Job ID: {parent_job.job_id}
        - Business Domain: {parent_job.business_domain}
        - Total Leads Requested: {parent_job.total_leads_requested}
        - Total Leads Scraped: {parent_job.total_leads_scraped}
        - Completion Time: {parent_job.actual_completion_time}

        You can view the detailed results in the admin panel.
        """

        msg.attach(MIMEText(body, 'plain'))


        with smtplib.SMTP(Config.email.SMTP_HOST, Config.email.SMTP_PORT, timeout=10) as server:
            server.starttls()
            server.login(Config.email.SMTP_USER, Config.email.SMTP_PASSWORD)
            server.send_message(msg)

    except Exception as e:
        log = JobLog(
            job_id=parent_job_id,
            job_type='parent',
            log_level='error',
            message=f'Error sending completion email: {str(e)}'
        )
        db.add(log)
        db.commit()
    finally:
        db.close()

def export_groups_to_csv(groups, file_path):
    """Export a list of group dicts to a CSV file."""
    fieldnames = ['group_name', 'group_link', 'member_count', 'platform']
    with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for group in groups:
            writer.writerow({
                'group_name': group.get('title', ''),
                'group_link': group.get('url', ''),
                'member_count': group.get('member_count', ''),
                'platform': group.get('platform', '')
            })

@dramatiq.actor(
    queue_name="group_member_scraping_tasks",
    priority=0,
    time_limit=3600000,
    max_retries=3,
    actor_name="process_group_member_scraping"
)
def process_group_member_scraping(child_job_id: str):
    """Process group member scraping after manual joining for Telegram/WhatsApp with retry mechanism."""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger("process_group_member_scraping")
    logger.info(f"process_group_member_scraping called with child_job_id={child_job_id}")
    
    db = get_db_session()
    try:
        child_job = db.query(ChildJob).filter(ChildJob.job_id == child_job_id).first()
        if not child_job:
            logger.error(f"Child job {child_job_id} not found")
            return

        # Update job status
        child_job.status = 'scraping_members'
        db.commit()

        # Get all group links for this job (both joined and pending - we'll try all)
        group_links = db.query(GroupLink).filter(
            GroupLink.child_job_id == child_job_id
        ).all()

        if not group_links:
            logger.warning(f"No groups found for job {child_job_id}")
            child_job.status = 'completed'
            child_job.completed_at = datetime.now()
            db.commit()
            return

        total_members_scraped = 0
        successful_groups = 0
        failed_groups = 0

        if child_job.platform == 'telegram':
            # Import Telegram scraper
            from data_collection.web_search.platform_hanlders.telegram import TelegramScraper, TelegramConfig
            
            # Get Telegram credentials from config
            config = TelegramConfig(
                api_id=Config.api_keys.TELEGRAM_API_ID or '27928447',
                api_hash=Config.api_keys.TELEGRAM_API_HASH or 'f9ab963bd50e9669e6ea7d7da4da1e0f',
                phone=Config.api_keys.TELEGRAM_PHONE or '+2349017491405'
            )
            
            scraper = TelegramScraper(config)
            
            async def scrape_telegram_members():
                nonlocal total_members_scraped, successful_groups, failed_groups
                
                try:
                    await scraper.connect()
                    
                    for group_link in group_links:
                        try:
                            logger.info(f"Attempting to scrape members from Telegram group: {group_link.group_name}")
                            
                            # Extract group identifier from link
                            group_identifier = group_link.group_link.split('/')[-1]
                            if not group_identifier.startswith('@'):
                                group_identifier = '@' + group_identifier
                            
                            members = await scraper.scrape_members(group_identifier)
                            
                            # Store scraped members
                            for member in members:
                                scraped_user = ScrapedUser(
                                    child_job_id=child_job_id,
                                    platform=child_job.platform,
                                    user_id=str(member.get('user_id', '')),
                                    username=member.get('username', ''),
                                    display_name=member.get('full_name', ''),
                                    bio=f"Group: {group_link.group_name}",
                                    profile_url=f"https://t.me/{member.get('username', '')}" if member.get('username') else None,
                                    additional_data={
                                        'group_name': group_link.group_name,
                                        'group_link': group_link.group_link,
                                        'first_name': member.get('first_name', ''),
                                        'last_name': member.get('last_name', ''),
                                        'group_id': member.get('group_id', '')
                                    }
                                )
                                db.add(scraped_user)
                                total_members_scraped += 1
                            
                            # Mark group as successfully scraped
                            group_link.join_status = 'scraped'
                            successful_groups += 1
                            db.commit()
                            logger.info(f"Successfully scraped {len(members)} members from {group_link.group_name}")
                            
                        except Exception as e:
                            logger.error(f"Error scraping members from {group_link.group_name}: {str(e)}")
                            failed_groups += 1
                            # Mark group as failed but continue with others
                            group_link.join_status = 'failed'
                            db.commit()
                            continue
                            
                finally:
                    await scraper.close()
            
            # Run the async function
            asyncio.run(scrape_telegram_members())

        elif child_job.platform == 'whatsapp':
            # Import WhatsApp scraper
            from data_collection.web_search.platform_hanlders.whatsapp import WhatsAppGroupScraper, WhatsAppConfig
            
            # Get WhatsApp credentials from config
            config = WhatsAppConfig(
                token=Config.api_keys.WHATSAPP_TOKEN or "IvvTzJXb9hPkunTFBOhptCi8WRDceQnU"
            )
            
            
            scraper = WhatsAppGroupScraper(config)
            
            try:
                # Check connection status
                status = scraper.check_connection_status()
                status_code = status.get('status', {}).get('code', 0)
                
                if status_code != 7:  # CONNECTED status
                    logger.error(f"WhatsApp not connected. Status code: {status_code}")
                    raise Exception("WhatsApp not connected")
                
                # Scrape all group members
                all_members = scraper.scrape_all_group_members()
                
                if all_members:
                    for group_name, participants in all_members.items():
                        logger.info(f"Processing {len(participants)} participants from {group_name}")
                        
                        for participant in participants:
                            scraped_user = ScrapedUser(
                                child_job_id=child_job_id,
                                platform=child_job.platform,
                                user_id=participant.get('id', ''),
                                username=participant.get('id', ''),
                                display_name=participant.get('name', ''),
                                bio=f"Group: {group_name}, Rank: {participant.get('rank', 'member')}",
                                additional_data={
                                    'group_name': group_name,
                                    'rank': participant.get('rank', 'member'),
                                    'phone': participant.get('phone', '')
                                }
                            )
                            db.add(scraped_user)
                            total_members_scraped += 1
                    
                    # Mark all groups as successfully scraped
                    for group_link in group_links:
                        group_link.join_status = 'scraped'
                    successful_groups = len(group_links)
                    
                    db.commit()
                    logger.info(f"Successfully scraped {total_members_scraped} total members from WhatsApp groups")
                else:
                    logger.warning("No WhatsApp group members found")
                    failed_groups = len(group_links)
                    for group_link in group_links:
                        group_link.join_status = 'failed'
                    db.commit()
                    
            except Exception as e:
                logger.error(f"Error scraping WhatsApp members: {str(e)}")
                failed_groups = len(group_links)
                for group_link in group_links:
                    group_link.join_status = 'failed'
                db.commit()
                raise

        # Update job status
        if successful_groups > 0:
            child_job.status = 'completed'
            child_job.completed_at = datetime.now()
            child_job.leads_scraped = total_members_scraped
            logger.info(f"Completed group member scraping for {child_job_id}. Total members: {total_members_scraped}, Successful groups: {successful_groups}, Failed groups: {failed_groups}")
        else:
            child_job.status = 'failed'
            child_job.error_message = f"Failed to scrape any groups. Successful: {successful_groups}, Failed: {failed_groups}"
            logger.error(f"Failed to scrape any groups for {child_job_id}")
        
        db.commit()

        # Check if all child jobs are completed
        check_parent_job_completion.send(child_job.parent_job_id)

    except Exception as e:
        logger.error(f"Error in process_group_member_scraping: {str(e)}")
        child_job.status = 'failed'
        child_job.error_message = str(e)
        db.commit()
        
        log = JobLog(
            job_id=child_job_id,
            job_type='child',
            log_level='error',
            message=f'Error processing group member scraping: {str(e)}'
        )
        db.add(log)
        db.commit()
        
        # Re-raise the exception to trigger retry
        raise
    finally:
        db.close() 