import os
from dotenv import load_dotenv
from typing import Optional

# Load environment variables from .env file
load_dotenv(override=True)

class DatabaseConfig:
    """Database configuration settings."""

    # PostgreSQL Database Configuration
    DATABASE_URL: str = os.getenv("DATABASE_URL", "postgresql://postgres:1234@localhost:5432/delegen_ai")
    DB_HOST: str = os.getenv("DB_HOST", "localhost")
    DB_PORT: str = os.getenv("DB_PORT", "5432")
    DB_USER: str = os.getenv("DB_USER", "postgres")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "1234")
    DB_NAME: str = os.getenv("DB_NAME", "delegen_ai")

    # AWS RDS Configuration (backup)
    AWS_DB_HOST: Optional[str] = os.getenv("AWS_DB_HOST")
    AWS_DB_NAME: Optional[str] = os.getenv("AWS_DB_NAME")
    AWS_DB_USER: Optional[str] = os.getenv("AWS_DB_USER")
    AWS_DB_PASSWORD: Optional[str] = os.getenv("AWS_DB_PASSWORD")

class RedisConfig:
    """Redis configuration for Dramatiq."""

    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_DB: int = int(os.getenv("REDIS_DB", "0"))
    REDIS_URL: str = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

class EmailConfig:
    """Email configuration for notifications."""

    SMTP_HOST: str = os.getenv("SMTP_HOST", "smtp.gmail.com")
    SMTP_PORT: int = int(os.getenv("SMTP_PORT", "587"))
    SMTP_USER: Optional[str] = os.getenv("SMTP_USER")
    SMTP_PASSWORD: Optional[str] = os.getenv("SMTP_PASSWORD")
    SMTP_FROM: Optional[str] = os.getenv("SMTP_FROM")
    ADMIN_EMAIL: Optional[str] = os.getenv("ADMIN_EMAIL")

class APIKeysConfig:
    """API keys configuration."""

    # Social Media API Keys
    INSTAGRAM_RAPID_API_KEY: Optional[str] = os.getenv("INSTAGRAM_RAPID_API_KEY")
    LINKEDIN_RAPID_API_KEY: Optional[str] = os.getenv("LINKEDIN_RAPID_API_KEY")
    TIKTOK_RAPID_API_KEY: Optional[str] = os.getenv("TIKTOK_RAPID_API_KEY")
    FACEBOOK_RAPID_API_KEY: Optional[str] = os.getenv("FACEBOOK_RAPID_API_KEY")
    
    TIKTOK_RAPID_API_HOST : Optional[str] = os.getenv("TIKTOK_RAPID_API_HOST", "tiktok-api23.p.rapidapi.com")
    APIFY_API_TOKEN: Optional[str] = os.getenv("APIFY_API_TOKEN", "**********************************************")
    APIFY_ACTOR_ID_INSTAGRAM: str = os.getenv("APIFY_ACTOR_ID_INSTAGRAM", "SbK00X0JYCPblD2wp")
    APIFY_ACTOR_ID_FACEBOOK: str = os.getenv("APIFY_ACTOR_ID_FACEBOOK", "us5srxAYnsrkgUv2v")
    APIFY_ACTOR_ID_LINKEDIN: str = os.getenv("APIFY_ACTOR_ID_LINKEDIN", "8xICdc1375BbZdgE3")
    APIFY_ACTOR_ID_TIKTOK: str = os.getenv("APIFY_ACTOR_ID_TIKTOK", "BDec00yAmCm1QbMEI")

    # Telegram API Keys
    TELEGRAM_API_ID: Optional[str] = os.getenv("TELEGRAM_API_ID", "27928447")
    TELEGRAM_API_HASH: Optional[str] = os.getenv("TELEGRAM_API_HASH", "f9ab963bd50e9669e6ea7d7da4da1e0f")
    TELEGRAM_PHONE: Optional[str] = os.getenv("TELEGRAM_PHONE", "+2349017491405")

    # WhatsApp API Keys
    WHATSAPP_TOKEN: Optional[str] = os.getenv("WHATSAPP_TOKEN", "IvvTzJXb9hPkunTFBOhptCi8WRDceQnU")

    # AI/ML API Keys
    OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY_VALID")
    TAVILY_API_KEY: Optional[str] = os.getenv("TAVILY_API_KEY")
    GROQ_API_KEY: Optional[str] = os.getenv("GROQ_API_KEY")

class AppConfig:
    """Application configuration."""

    # Application Settings
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    API_HOST: str = os.getenv("API_HOST", "0.0.0.0")
    API_PORT: int = int(os.getenv("API_PORT", "8080"))

    # Dramatiq Worker Settings
    DRAMATIQ_PROCESSES: int = int(os.getenv("DRAMATIQ_PROCESSES", "1"))
    DRAMATIQ_THREADS: int = int(os.getenv("DRAMATIQ_THREADS", "1"))

    # Logging Settings
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "app.log")

class Config:
    """Main configuration class that combines all config sections."""

    # Configuration sections
    database = DatabaseConfig()
    redis = RedisConfig()
    email = EmailConfig()
    api_keys = APIKeysConfig()
    app = AppConfig()

    # Legacy support for existing code
    TAVILY_API_KEY = api_keys.TAVILY_API_KEY
    OPENAI_API_KEY = api_keys.OPENAI_API_KEY
   

    @classmethod
    def validate_required_settings(cls) -> list:
        """Validate that all required settings are present."""
        missing_settings = []

        # Check database settings
        if not cls.database.DATABASE_URL:
            missing_settings.append("DATABASE_URL")

        # Check Redis settings
        if not cls.redis.REDIS_HOST:
            missing_settings.append("REDIS_HOST")

        # Check critical API keys
        if not cls.api_keys.OPENAI_API_KEY and not cls.api_keys.OPENAI_API_KEY_v2:
            missing_settings.append("OPENAI_API_KEY or OPENAI_API_KEY_v2")

        return missing_settings

    @classmethod
    def get_database_url(cls, use_aws: bool = False) -> str:
        """Get database URL, optionally using AWS RDS."""
        if use_aws and cls.database.AWS_DB_HOST:
            return f"postgresql://{cls.database.AWS_DB_USER}:{cls.database.AWS_DB_PASSWORD}@{cls.database.AWS_DB_HOST}/{cls.database.AWS_DB_NAME}"
        return cls.database.DATABASE_URL

    @classmethod
    def print_config_summary(cls):
        """Print a summary of the current configuration (without sensitive data)."""
        print("=== Configuration Summary ===")
        print(f"Database Host: {cls.database.DB_HOST}:{cls.database.DB_PORT}")
        print(f"Database Name: {cls.database.DB_NAME}")
        print(f"Redis Host: {cls.redis.REDIS_HOST}:{cls.redis.REDIS_PORT}")
        print(f"API Host: {cls.app.API_HOST}:{cls.app.API_PORT}")
        print(f"Debug Mode: {cls.app.DEBUG}")
        print(f"Log Level: {cls.app.LOG_LEVEL}")

        # Check API keys availability (without showing actual keys)
        api_keys_status = {
            "OpenAI": "✓" if cls.api_keys.OPENAI_API_KEY or cls.api_keys.OPENAI_API_KEY_v2 else "✗",
            "Tavily": "✓" if cls.api_keys.TAVILY_API_KEY else "✗",
            "Instagram": "✓" if cls.api_keys.INSTAGRAM_RAPID_API_KEY else "✗",
            "LinkedIn": "✓" if cls.api_keys.LINKEDIN_RAPID_API_KEY else "✗",
            "TikTok": "✓" if cls.api_keys.TIKTOK_RAPID_API_KEY else "✗",
        }

        print("API Keys Status:")
        for service, status in api_keys_status.items():
            print(f"  {service}: {status}")
        print("=" * 30)
