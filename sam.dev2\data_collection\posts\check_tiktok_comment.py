from comment_scraper import CommentScraper, SocialMediaPlatform, format_comments_for_output
import os
import json

API_TOKEN = os.getenv("APIFY_API_TOKEN", "**********************************************")

# Replace with a public TikTok post URL
TIKTOK_POST_URL = "https://www.tiktok.com/@bellapoarch/video/6862153058223197445"

scraper = CommentScraper(API_TOKEN)
comments = scraper.scrape_comments(
    SocialMediaPlatform.TIKTOK,
    [TIKTOK_POST_URL],
    comments_per_post=1  # Only fetch 1 comment to save quota
)
formatted = format_comments_for_output(comments)

# Print the raw structure of the first comment
for post_url, comment_list in formatted.items():
    print(f"Post URL: {post_url}")
    if comment_list:
        print("First comment JSON:")
        print(json.dumps(comment_list[0], indent=2))
    else:
        print("No comments found.")