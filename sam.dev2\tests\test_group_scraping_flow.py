#!/usr/bin/env python3
"""
Test script to demonstrate the new simplified group scraping flow for Telegram and WhatsApp.

This script shows how to:
1. Create a scraping job for Telegram/WhatsApp
2. Check pending groups that need manual joining
3. Complete manual action and trigger member scraping (simplified)
4. Check job status
"""

import requests
import json
import time
from typing import Dict, Any

# API base URL
BASE_URL = "http://localhost:8080"

def create_scraping_job(platform: str) -> Dict[str, Any]:
    """Create a scraping job for the specified platform."""
    
    payload = {
        "user_id": "test_user_123",
        "business_domain": "Technology",
        "business_goals": "Lead generation for software development services",
        "target_market": "Small to medium businesses",
        "business_website_url": "https://example.com",
        "target_platforms": [platform],
        "leads_per_platform": {platform: 50},
        "additional_criteria": "Interested in software development, IT services"
    }
    
    response = requests.post(f"{BASE_URL}/api/scraping/create-job", json=payload)
    response.raise_for_status()
    
    return response.json()

def get_pending_groups(parent_job_id: str, platform: str) -> Dict[str, Any]:
    """Get list of groups that need manual joining for a specific parent job."""
    
    response = requests.get(f"{BASE_URL}/api/scraping/pending-groups/{parent_job_id}/{platform}")
    response.raise_for_status()
    
    return response.json()

def complete_manual_action(parent_job_id: str, platform: str) -> Dict[str, Any]:
    """Mark manual action as completed and trigger member scraping for all groups."""
    
    payload = {
        "parent_job_id": parent_job_id,
        "platform": platform
    }
    
    response = requests.post(f"{BASE_URL}/api/scraping/manual-action-completed", json=payload)
    response.raise_for_status()
    
    return response.json()

def get_job_status(job_id: str) -> Dict[str, Any]:
    """Get the status of a job."""
    
    response = requests.get(f"{BASE_URL}/api/scraping/job/{job_id}")
    response.raise_for_status()
    
    return response.json()

def get_all_scraped_users(parent_job_id: str) -> Dict[str, Any]:
    """Get all scraped users for a parent job, organized by platform."""
    
    response = requests.get(f"{BASE_URL}/api/scraping/users/{parent_job_id}")
    response.raise_for_status()
    
    return response.json()

def get_scraped_users_by_platform(parent_job_id: str, platform: str) -> Dict[str, Any]:
    """Get scraped users for a specific platform within a parent job."""
    
    response = requests.get(f"{BASE_URL}/api/scraping/users/{parent_job_id}/{platform}")
    response.raise_for_status()
    
    return response.json()

def test_scraped_users_endpoints():
    """Test the scraped users endpoints."""
    print("\n=== Testing Scraped Users Endpoints ===")
    
    try:
        # Create a test job with multiple platforms
        print("1. Creating test job with multiple platforms...")
        payload = {
            "user_id": "test_user_456",
            "business_domain": "Marketing",
            "business_goals": "Lead generation for digital marketing services",
            "target_market": "E-commerce businesses",
            "business_website_url": "https://marketing-example.com",
            "target_platforms": ["instagram", "linkedin"],
            "leads_per_platform": {"instagram": 20, "linkedin": 15},
            "additional_criteria": "Interested in digital marketing, social media"
        }
        
        response = requests.post(f"{BASE_URL}/api/scraping/create-job", json=payload)
        response.raise_for_status()
        job_response = response.json()
        
        parent_job_id = job_response["parent_job_id"]
        print(f"   Parent Job ID: {parent_job_id}")
        
        # Wait for processing
        print("2. Waiting for job processing...")
        time.sleep(5)
        
        # Test getting all scraped users
        print("3. Testing get all scraped users endpoint...")
        all_users = get_all_scraped_users(parent_job_id)
        print(f"   Total users: {all_users['total_users']}")
        print(f"   Platforms: {[p['platform'] for p in all_users['platforms']]}")
        print(f"   Summary: {all_users['summary']}")
        
        # Test getting users by platform
        for platform in ["instagram", "linkedin"]:
            print(f"4. Testing get users by platform ({platform})...")
            platform_users = get_scraped_users_by_platform(parent_job_id, platform)
            print(f"   {platform} users: {platform_users['total_users']}")
            print(f"   {platform} child jobs: {len(platform_users['child_jobs'])}")
            
            if platform_users['users']:
                print(f"   Sample user: {platform_users['users'][0]['username']}")
        
        # Test with non-existent job
        print("5. Testing with non-existent job...")
        try:
            non_existent = get_all_scraped_users("job_nonexistent")
            print("   Unexpected success with non-existent job")
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                print("   Correctly returned 404 for non-existent job")
            else:
                print(f"   Unexpected error: {e}")
        
    except Exception as e:
        print(f"Error in scraped users test: {str(e)}")

def test_telegram_flow():
    """Test the complete Telegram scraping flow."""
    print("=== Testing Telegram Scraping Flow ===")
    
    try:
        # Step 1: Create scraping job
        print("1. Creating Telegram scraping job...")
        job_response = create_scraping_job("telegram")
        parent_job_id = job_response["parent_job_id"]
        child_job_id = job_response["child_job_ids"]["telegram"]
        print(f"   Parent Job ID: {parent_job_id}")
        print(f"   Child Job ID: {child_job_id}")
        
        # Step 2: Wait a bit for the job to process
        print("2. Waiting for job processing...")
        time.sleep(5)
        
        # Step 3: Check job status
        print("3. Checking job status...")
        status = get_job_status(child_job_id)
        print(f"   Status: {status['status']}")
        
        # Step 4: Get pending groups
        print("4. Getting pending groups...")
        pending_groups = get_pending_groups(parent_job_id, "telegram")
        print(f"   Found {pending_groups['total_groups']} pending groups")
        
        if pending_groups['pending_groups']:
            print("   Group preview:")
            for group in pending_groups['pending_groups'][:3]:  # Show first 3 groups
                print(f"     - {group['group_name']}: {group['group_link']} ({group['member_count']} members)")
            
            # Step 5: Complete manual action (simplified - no need to specify groups)
            print("5. Completing manual action (assuming user has joined the groups)...")
            manual_action_response = complete_manual_action(parent_job_id, "telegram")
            print(f"   Manual action completed: {manual_action_response['message']}")
            print(f"   Groups found: {manual_action_response['groups_found']}")
            print(f"   Child job IDs: {manual_action_response['child_job_ids']}")
            
            # Step 6: Wait for member scraping to complete
            print("6. Waiting for member scraping to complete...")
            time.sleep(15)  # Give more time for retry mechanism
            
            # Step 7: Check final status
            print("7. Checking final job status...")
            final_status = get_job_status(child_job_id)
            print(f"   Final Status: {final_status['status']}")
            print(f"   Leads Scraped: {final_status.get('leads_scraped', 0)}")
            
            # Step 8: Check parent job status
            print("8. Checking parent job status...")
            parent_status = get_job_status(parent_job_id)
            print(f"   Parent Status: {parent_status['status']}")
            print(f"   Total Leads Scraped: {parent_status.get('total_leads_scraped', 0)}")
            
            # Step 9: Test scraped users endpoints
            print("9. Testing scraped users endpoints...")
            all_users = get_all_scraped_users(parent_job_id)
            print(f"   Total scraped users: {all_users['total_users']}")
            
            telegram_users = get_scraped_users_by_platform(parent_job_id, "telegram")
            print(f"   Telegram users: {telegram_users['total_users']}")
        
    except Exception as e:
        print(f"Error in Telegram flow: {str(e)}")

def test_whatsapp_flow():
    """Test the complete WhatsApp scraping flow."""
    print("\n=== Testing WhatsApp Scraping Flow ===")
    
    try:
        # Step 1: Create scraping job
        print("1. Creating WhatsApp scraping job...")
        job_response = create_scraping_job("whatsapp")
        parent_job_id = job_response["parent_job_id"]
        child_job_id = job_response["child_job_ids"]["whatsapp"]
        print(f"   Parent Job ID: {parent_job_id}")
        print(f"   Child Job ID: {child_job_id}")
        
        # Step 2: Wait a bit for the job to process
        print("2. Waiting for job processing...")
        time.sleep(5)
        
        # Step 3: Check job status
        print("3. Checking job status...")
        status = get_job_status(child_job_id)
        print(f"   Status: {status['status']}")
        
        # Step 4: Get pending groups
        print("4. Getting pending groups...")
        pending_groups = get_pending_groups(parent_job_id, "whatsapp")
        print(f"   Found {pending_groups['total_groups']} pending groups")
        
        if pending_groups['pending_groups']:
            print("   Group preview:")
            for group in pending_groups['pending_groups'][:3]:  # Show first 3 groups
                print(f"     - {group['group_name']}: {group['group_link']} ({group['member_count']} members)")
            
            # Step 5: Complete manual action (simplified - no need to specify groups)
            print("5. Completing manual action (assuming user has joined the groups)...")
            manual_action_response = complete_manual_action(parent_job_id, "whatsapp")
            print(f"   Manual action completed: {manual_action_response['message']}")
            print(f"   Groups found: {manual_action_response['groups_found']}")
            print(f"   Child job IDs: {manual_action_response['child_job_ids']}")
            
            # Step 6: Wait for member scraping to complete
            print("6. Waiting for member scraping to complete...")
            time.sleep(15)  # Give more time for retry mechanism
            
            # Step 7: Check final status
            print("7. Checking final job status...")
            final_status = get_job_status(child_job_id)
            print(f"   Final Status: {final_status['status']}")
            print(f"   Leads Scraped: {final_status.get('leads_scraped', 0)}")
            
            # Step 8: Check parent job status
            print("8. Checking parent job status...")
            parent_status = get_job_status(parent_job_id)
            print(f"   Parent Status: {parent_status['status']}")
            print(f"   Total Leads Scraped: {parent_status.get('total_leads_scraped', 0)}")
        
    except Exception as e:
        print(f"Error in WhatsApp flow: {str(e)}")

def main():
    """Run the test flows."""
    print("Starting simplified group scraping flow tests...")
    print("Make sure the API server is running on localhost:8080")
    print("=" * 60)
    
    # Test scraped users endpoints
    test_scraped_users_endpoints()
    
    # Test Telegram flow
    test_telegram_flow()
    
    # Test WhatsApp flow
    test_whatsapp_flow()
    
    print("\n" + "=" * 60)
    print("Test completed!")
    print("\nKey improvements in this version:")
    print("- Users only need to provide parent_job_id and platform")
    print("- System automatically finds all group links")
    print("- Built-in retry mechanism (3 retries with exponential backoff)")
    print("- Automatic handling of successful/failed group scraping")
    print("- Better error handling and status tracking")
    print("- New endpoints to retrieve scraped users by platform")

if __name__ == "__main__":
    main() 