import requests
import json
import time
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
import logging
from enum import Enum

from logger_config import setup_logger

logger = setup_logger()

class ConnectionStatus(Enum):
    """Enum for WhatsApp connection status codes"""
    WAITING_QR = 3
    ERROR = 5
    CONNECTED = 7

@dataclass
class WhatsAppConfig:
    """Configuration for WhatsApp client"""
    base_url: str = "https://gate.whapi.cloud"
    token: Optional[str] = None

@dataclass
class WhatsAppParticipant:
    """Data structure for WhatsApp group participant"""
    id: str
    rank: str = "member"
    name: Optional[str] = None
    phone: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert participant data to dictionary"""
        return {
            'id': self.id,
            'rank': self.rank,
            'name': self.name,
            'phone': self.phone
        }

@dataclass
class WhatsAppGroup:
    """Data structure for WhatsApp group"""
    id: str
    name: str
    participants: List[WhatsAppParticipant]

    def to_dict(self) -> Dict[str, Any]:
        """Convert group data to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'participants': [p.to_dict() for p in self.participants]
        }

class WhatsAppGroupScraper:
    """WhatsApp scraper for collecting group and member information"""
    
    def __init__(self, config: WhatsAppConfig):
        self.config = config
        self.headers = {
            "accept": "application/json",
            "content-type": "application/json"
        }
    
    def _make_request(self, endpoint: str, method: str = "GET", data: Optional[Dict] = None) -> Dict:
        """
        Make HTTP request to WhatsApp API
        
        Args:
            endpoint: API endpoint
            method: HTTP method
            data: Request data for POST/PUT requests
            
        Returns:
            Dict: API response data
        """
        if not self.config.token:
            raise ValueError("No authentication token set")
            
        url = f"{self.config.base_url}/{endpoint}?token={self.config.token}"
        
        try:
            if method == "GET":
                response = requests.get(url, headers=self.headers)
            else:
                response = requests.post(url, headers=self.headers, json=data)
                
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {str(e)}")
            raise
    
    def get_qr_code(self) -> Dict:
        """
        Get QR code for device linking
        
        Returns:
            Dict: QR code data
        """
        try:
            qr_data = self._make_request("users/login")
            logger.info("QR code retrieved successfully")
            return qr_data
        except Exception as e:
            logger.error(f"Failed to get QR code: {str(e)}")
            return {}
    
    def check_connection_status(self) -> Dict:
        """
        Check WhatsApp connection status
        
        Returns:
            Dict: Connection status information
        """
        try:
            status_data = self._make_request("health")
            status_code = status_data.get('status', {}).get('code', 0)
            
            if status_code == ConnectionStatus.WAITING_QR.value:
                logger.info("Status: Waiting for QR code scan")
            elif status_code == ConnectionStatus.ERROR.value:
                logger.error("Status: Error - Device not connected")
            elif status_code == ConnectionStatus.CONNECTED.value:
                logger.info("Status: Connected and ready!")
            else:
                logger.warning(f"Status: Unknown (Code: {status_code})")
            
            return status_data
            
        except Exception as e:
            logger.error(f"Failed to check status: {str(e)}")
            return {}
    
    def get_user_groups(self) -> List[WhatsAppGroup]:
        """
        Get all groups the authenticated user is part of
        
        Returns:
            List[WhatsAppGroup]: List of groups
        """
        try:
            groups_data = self._make_request("groups")
            groups = groups_data.get('groups', []) if isinstance(groups_data, dict) else groups_data
            
            logger.info(f"Found {len(groups)} groups")
            return [
                WhatsAppGroup(
                    id=group.get('id'),
                    name=group.get('name', 'Unknown Group'),
                    participants=[]
                )
                for group in groups
            ]
            
        except Exception as e:
            logger.error(f"Failed to get groups: {str(e)}")
            return []
    
    def get_group_details(self, group: WhatsAppGroup) -> Optional[WhatsAppGroup]:
        """
        Get detailed information about a specific group
        
        Args:
            group: WhatsAppGroup object
            
        Returns:
            Optional[WhatsAppGroup]: Updated group with participants
        """
        try:
            group_details = self._make_request(f"groups/{group.id}")
            participants = group_details.get('participants', [])
            
            group.participants = [
                WhatsAppParticipant(
                    id=p.get('id'),
                    rank=p.get('rank', 'member'),
                    name=p.get('name'),
                    phone=p.get('phone')
                )
                for p in participants
            ]
            
            logger.info(f"Group '{group.name}' has {len(participants)} participants")
            return group
            
        except Exception as e:
            logger.error(f"Failed to get group details for {group.id}: {str(e)}")
            return None
    
    def scrape_all_group_members(self) -> Dict[str, List[Dict]]:
        """
        Scrape all members from all groups
        
        Returns:
            Dict: Dictionary with group names as keys and participant lists as values
        """
        logger.info("Starting group member scraping...")
        
        groups = self.get_user_groups()
        if not groups:
            logger.error("No groups found or failed to retrieve groups")
            return {}
        
        all_group_members = {}
        
        for group in groups:
            logger.info(f"Processing group: {group.name}")
            
            group_with_details = self.get_group_details(group)
            if group_with_details:
                all_group_members[group.name] = [
                    participant.to_dict() 
                    for participant in group_with_details.participants
                ]
            
            time.sleep(1)  # Rate limiting
        
        return all_group_members
    
    def get_participant_summary(self, all_group_members: Dict[str, List[Dict]]) -> Dict:
        """
        Generate summary statistics of all participants
        
        Args:
            all_group_members: Group members data
            
        Returns:
            Dict: Summary statistics
        """
        total_groups = len(all_group_members)
        all_participants = set()
        admin_count = 0
        
        for group_name, participants in all_group_members.items():
            for participant in participants:
                participant_id = participant.get('id')
                rank = participant.get('rank', 'member')
                
                if participant_id:
                    all_participants.add(participant_id)
                
                if rank in ['admin', 'creator']:
                    admin_count += 1
        
        return {
            "total_groups": total_groups,
            "unique_participants": len(all_participants),
            "total_admin_roles": admin_count,
            "groups_breakdown": {
                group: len(participants) 
                for group, participants in all_group_members.items()
            }
        }

async def main():
    """Main function to run the scraper"""
    config = WhatsAppConfig(
        token="IvvTzJXb9hPkunTFBOhptCi8WRDceQnU"  # Replace with your token
    )
    
    scraper = WhatsAppGroupScraper(config)
    
    try:
        # Check connection status
        status = scraper.check_connection_status()
        status_code = status.get('status', {}).get('code', 0)
        
        if status_code != ConnectionStatus.CONNECTED.value:
            logger.warning(f"Status Code {status_code} - Not fully connected")
        
        # Scrape members
        all_members = scraper.scrape_all_group_members()
        
        if all_members:
            # Generate and save summary
            summary = scraper.get_participant_summary(all_members)
            
            # Save data
            with open("whatsapp_group_members.json", "w") as f:
                json.dump(all_members, f, indent=2)
            
            with open("group_summary.json", "w") as f:
                json.dump(summary, f, indent=2)
            
            return all_members
            
    except Exception as e:
        logger.error(f"Scraping failed: {str(e)}")
        return {}

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())