# Lead Generation Bot

A powerful, multi-platform social media lead generation system that automatically scrapes and collects potential leads from various social media platforms including Instagram, Facebook, LinkedIn, TikTok, Telegram, and WhatsApp.

## 🚀 Features

### Multi-Platform Support
- **Direct API Platforms**: Instagram, Facebook, LinkedIn, TikTok
- **Group-Based Platforms**: Telegram, WhatsApp (with manual group joining)
- **Web Search Integration**: Automated discovery of relevant groups and profiles

### Advanced Scraping Capabilities
- **Intelligent Lead Discovery**: AI-powered search for relevant profiles
- **Comment Analysis**: Extract leads from social media comments
- **Group Member Scraping**: Collect members from Telegram/WhatsApp groups
- **Profile Enrichment**: Comprehensive user profile data collection

### Robust Architecture
- **Async Processing**: Background job processing with Dramatiq
- **Retry Mechanism**: Automatic retry with exponential backoff
- **Error Handling**: Comprehensive error handling and logging
- **Database Storage**: PostgreSQL for reliable data persistence
- **Redis Caching**: Fast caching and message queuing

### User-Friendly API
- **RESTful API**: Clean, documented API endpoints
- **Job Management**: Create, monitor, and manage scraping jobs
- **Real-time Status**: Track job progress and completion
- **Data Export**: Retrieve scraped users by platform

## 📋 Prerequisites

- Python 3.8+
- PostgreSQL 12+
- Redis 6+
- Various API keys (see Configuration section)

## 🛠️ Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd Lead-Genbot
```

### 2. Run Setup Script
```bash
chmod +x setup.sh
./setup.sh
```

### 3. Configure Environment
```bash
cp env.example .env
# Edit .env with your actual API keys and credentials
```

### 4. Start Services
```bash
# Start PostgreSQL (if not already running)
brew services start postgresql  # macOS
sudo systemctl start postgresql  # Linux

# Start Redis
brew services start redis  # macOS
sudo systemctl start redis-server  # Linux
```

### 5. Initialize Database
```bash
python3 setup_database.py
```

### 6. Start the Application
```bash
# Option 1: Using main.py (Recommended)
# Terminal 1: Start the API server
python3 main.py api

# Terminal 2: Start Dramatiq workers
python3 main.py worker

# Option 2: Run both API and worker in separate processes
python3 main.py both

# Option 3: Using the original commands
# Terminal 1: Start the API server
python3 run.py

# Terminal 2: Start Dramatiq workers
dramatiq app.tasks
```

### 7. Test the Setup
```bash
python3 test_setup.py
python3 test_group_scraping_flow.py
```

## 🔧 Configuration

### Environment Variables

Copy `env.example` to `.env` and configure the following:

#### Database Configuration
```env
DATABASE_URL=postgresql://postgres:password@localhost:5432/delegen_ai
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=delegen_ai
```

#### Redis Configuration
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
```

#### API Keys
```env
# OpenAI
OPENAI_API_KEY_VALID=your_openai_api_key

# Tavily (for web search)
TAVILY_API_KEY=your_tavily_api_key

# Social Media APIs
INSTAGRAM_RAPID_API_KEY=your_instagram_key
LINKEDIN_RAPID_API_KEY=your_linkedin_key
TIKTOK_RAPID_API_KEY=your_tiktok_key
FACEBOOK_RAPID_API_KEY=your_facebook_key

# Telegram
TELEGRAM_API_ID=your_telegram_api_id
TELEGRAM_API_HASH=your_telegram_api_hash
TELEGRAM_PHONE=your_phone_number

# WhatsApp
WHATSAPP_TOKEN=your_whatsapp_token
```

#### Email Configuration
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM=<EMAIL>
ADMIN_EMAIL=<EMAIL>
```

## 📚 API Documentation

### Core Endpoints

#### Create Scraping Job
```bash
POST /api/scraping/create-job
```

**Request:**
```json
{
    "user_id": "user123",
    "business_domain": "Technology",
    "business_goals": "Lead generation for software development",
    "target_market": "Small to medium businesses",
    "business_website_url": "https://example.com",
    "target_platforms": ["instagram", "telegram"],
    "leads_per_platform": {"instagram": 50, "telegram": 30},
    "additional_criteria": "Interested in software development"
}
```

#### Get Job Status
```bash
GET /api/scraping/job/{job_id}
```

#### Get All Scraped Users
```bash
GET /api/scraping/users/{parent_job_id}
```

#### Get Users by Platform
```bash
GET /api/scraping/users/{parent_job_id}/{platform}
```

### Group-Based Platform Endpoints

#### Get Pending Groups
```bash
GET /api/scraping/pending-groups/{parent_job_id}/{platform}
```

#### Complete Manual Action
```bash
POST /api/scraping/manual-action-completed
```

**Request:**
```json
{
    "parent_job_id": "job_abc123",
    "platform": "telegram"
}
```

## 🔄 Workflow

### Direct API Platforms (Instagram, Facebook, LinkedIn, TikTok)
1. **Job Creation**: User creates scraping job with target criteria
2. **Automatic Processing**: System automatically scrapes leads
3. **Data Storage**: Results stored in database
4. **Completion**: Job marked as completed

### Group-Based Platforms (Telegram, WhatsApp)
1. **Job Creation**: User creates scraping job
2. **Group Discovery**: System finds relevant groups
3. **Manual Joining**: User manually joins groups
4. **Member Scraping**: System scrapes members from joined groups
5. **Completion**: Results stored and job completed

## 📱 Group-Based Platform Scraping

Unlike other social media platforms that can be scraped directly via APIs, Telegram and WhatsApp require users to manually join groups before member information can be collected. This implementation provides a two-phase approach with simplified user experience.

### Overview

The group-based scraping system works differently from direct API platforms:

1. **Phase 1**: Find relevant groups and notify users to join them
2. **Phase 2**: After manual joining, scrape member information from all groups with retry mechanism

### Detailed Workflow

#### 1. Job Creation
When a user creates a scraping job for Telegram or WhatsApp:

```bash
POST /api/scraping/create-job
{
    "user_id": "user123",
    "business_domain": "Technology",
    "business_goals": "Lead generation for software development",
    "target_market": "Small to medium businesses",
    "business_website_url": "https://example.com",
    "target_platforms": ["telegram"],
    "leads_per_platform": {"telegram": 50},
    "additional_criteria": "Interested in software development"
}
```

#### 2. Group Discovery
The system automatically:
- Searches for relevant groups using the business criteria
- Stores group links in the database
- Sets job status to `awaiting_manual_action`
- Sends email notification with group previews

#### 3. Manual Group Joining
Users receive an email with group links and must manually:
- Join the relevant groups on Telegram/WhatsApp
- Note which groups they successfully joined

#### 4. Trigger Member Scraping (Simplified)
After joining groups, users call the manual action completion endpoint with just the parent job ID and platform:

```bash
POST /api/scraping/manual-action-completed
{
    "parent_job_id": "job_abc123",
    "platform": "telegram"
}
```

#### 5. Member Scraping with Retry
The system automatically:
- Finds all group links for the specified parent job and platform
- Attempts to scrape members from all groups
- Uses retry mechanism (3 retries with exponential backoff)
- Handles successful and failed group scraping separately
- Stores member data in the database
- Updates job status to `completed` or `failed`

### Key Features

#### Simplified User Experience
- Users only need to provide `parent_job_id` and `platform`
- No need to manually specify which groups were joined
- System automatically finds all group links for the job

#### Retry Mechanism
- Built-in retry mechanism with exponential backoff
- 3 retry attempts with increasing delays (1s, 2s, 4s)
- Handles temporary connection issues and rate limiting

#### Robust Error Handling
- Individual group failure doesn't stop the entire process
- Tracks successful vs failed groups separately
- Detailed logging for troubleshooting

#### Status Tracking
- Group status: `pending` → `joined` → `scraped`/`failed`
- Job status: `awaiting_manual_action` → `scraping_members` → `completed`/`failed`
- Comprehensive error messages and logging

### API Endpoints for Group Scraping

#### Get Pending Groups by Parent Job
```bash
GET /api/scraping/pending-groups/{parent_job_id}/{platform}
```

Returns groups that need manual joining for the specified parent job and platform.

**Response:**
```json
{
    "parent_job_id": "job_abc123",
    "platform": "telegram",
    "pending_groups": [
        {
            "child_job_id": "job_def456",
            "group_name": "Tech Developers",
            "group_link": "https://t.me/techdevelopers",
            "member_count": 1500,
            "join_status": "pending"
        }
    ],
    "total_groups": 1
}
```

#### Complete Manual Action (Simplified)
```bash
POST /api/scraping/manual-action-completed
```

Marks all groups as joined and triggers member scraping with retry mechanism.

**Request:**
```json
{
    "parent_job_id": "job_abc123",
    "platform": "telegram"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Manual action completed. Started scraping 5 groups for telegram with retry mechanism.",
    "parent_job_id": "job_abc123",
    "platform": "telegram",
    "groups_found": 5,
    "child_job_ids": ["job_def456", "job_ghi789"]
}
```

### Configuration for Group-Based Platforms

#### Telegram Configuration
Add these environment variables to your `.env` file:

```env
TELEGRAM_API_ID=27928447
TELEGRAM_API_HASH=f9ab963bd50e9669e6ea7d7da4da1e0f
TELEGRAM_PHONE=+2349017491405
```

#### WhatsApp Configuration
Add this environment variable to your `.env` file:

```env
WHATSAPP_TOKEN=IvvTzJXb9hPkunTFBOhptCi8WRDceQnU
```

### Database Schema for Group Scraping

#### GroupLink Table
Stores information about groups that need manual joining:

```sql
CREATE TABLE group_links (
    id SERIAL PRIMARY KEY,
    child_job_id VARCHAR(50) REFERENCES child_jobs(job_id),
    platform VARCHAR(20),
    group_name VARCHAR(255),
    group_link VARCHAR(500),
    group_id VARCHAR(100),
    member_count INTEGER,
    join_status VARCHAR(50) DEFAULT 'pending',
    manual_join_required BOOLEAN DEFAULT TRUE,
    scraped_at TIMESTAMP DEFAULT NOW()
);
```

#### Job Status Flow
1. `scheduled` → Initial job creation
2. `in_progress` → Job processing started
3. `awaiting_manual_action` → Groups found, waiting for user to join
4. `scraping_members` → Manual action completed, scraping members
5. `completed` → Member scraping finished
6. `failed` → Error occurred during processing

#### Group Status Flow
1. `pending` → Group discovered, waiting for joining
2. `joined` → User has joined the group
3. `scraped` → Successfully scraped members from group
4. `failed` → Failed to scrape members from group

### Error Handling for Group Scraping

#### Common Issues

1. **No groups found**: The system couldn't find relevant groups for the business criteria
2. **Connection failed**: Telegram/WhatsApp credentials are invalid or connection issues
3. **Permission denied**: The bot doesn't have permission to access group members
4. **Rate limiting**: Too many requests to the platform APIs
5. **Partial failures**: Some groups succeed while others fail

#### Troubleshooting

1. **Check credentials**: Verify Telegram/WhatsApp API credentials are correct
2. **Verify group links**: Ensure group links are valid and accessible
3. **Check job logs**: Review the `job_logs` table for detailed error messages
4. **Monitor status**: Use the job status endpoint to track progress
5. **Retry mechanism**: The system automatically retries failed attempts

### Security Considerations for Group Scraping

1. **API Credentials**: Store sensitive credentials in environment variables
2. **Rate Limiting**: Implement appropriate delays between API calls
3. **Data Privacy**: Ensure compliance with platform terms of service
4. **Access Control**: Validate user permissions before allowing group scraping
5. **Error Logging**: Avoid logging sensitive information in error messages

### Testing Group Scraping

Use the provided test script to verify the functionality:

```bash
python test_group_scraping_flow.py
```

This script demonstrates the complete simplified flow:
1. Creating a scraping job
2. Checking pending groups
3. Completing manual action (simplified)
4. Monitoring job status with retry mechanism

## 📊 Data Models

### ParentJob
- Main job container for multi-platform scraping
- Contains business criteria and overall status

### ChildJob
- Individual platform-specific job
- Tracks platform-specific progress and results

### ScrapedUser
- Individual user profile data
- Contains all scraped information

### GroupLink
- Group information for Telegram/WhatsApp
- Tracks joining and scraping status

## 🧪 Testing

### Run All Tests
```bash
python3 test_setup.py
python3 test_group_scraping_flow.py
```

### Test Individual Components
```bash
# Test database connection
python3 -c "from app.database import test_db_connection; print(test_db_connection())"

# Test Redis connection
python3 -c "import redis; r = redis.Redis(); print(r.ping())"

# Test API endpoints
curl http://localhost:8080/health
```

## 🚀 Deployment

### Production Setup
1. **Environment**: Use production-grade PostgreSQL and Redis
2. **Security**: Set strong passwords and API keys
3. **Monitoring**: Enable logging and monitoring
4. **Scaling**: Use multiple Dramatiq workers

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d
```

### Cloud Deployment
- **AWS**: Use RDS for PostgreSQL, ElastiCache for Redis
- **GCP**: Use Cloud SQL and Memorystore
- **Azure**: Use Azure Database and Azure Cache

## 🔒 Security

### Best Practices
- Never commit `.env` files to version control
- Use strong, unique passwords
- Rotate API keys regularly
- Monitor API usage and costs
- Implement rate limiting
- Use HTTPS in production

### API Security
- Validate all input data
- Implement proper error handling
- Use environment-specific configurations
- Monitor for suspicious activity

## 📈 Monitoring

### Health Checks
```bash
curl http://localhost:8080/health
```

### Logs
- Application logs: `logs/app.log`
- Error logs: `logs/error.log`
- Job logs: Stored in database

### Metrics
- Job completion rates
- API response times
- Error rates
- Database performance

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

### Development Setup
```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run tests
pytest

# Run linting
flake8
black .
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Common Issues

#### Database Connection Issues
- Check PostgreSQL is running
- Verify database credentials in `.env`
- Ensure database exists

#### Redis Connection Issues
- Check Redis is running
- Verify Redis configuration
- Test connection: `redis-cli ping`

#### API Key Issues
- Verify API keys are valid
- Check rate limits
- Ensure proper permissions

#### Group Scraping Issues
- Verify Telegram/WhatsApp credentials
- Check group accessibility
- Ensure manual joining completed

### Getting Help
- Check the Group-Based Platform Scraping section above
- Review the API documentation at `http://localhost:8080/docs`
- Check the logs for error details
- Open an issue on GitHub

## 🔗 Useful Links

- [API Documentation](http://localhost:8080/docs)
- [Health Check](http://localhost:8080/health)
- [Environment Configuration](env.example)
- [Setup Script](setup.sh)

## 🎯 Roadmap

- [ ] Web interface for job management
- [ ] Advanced filtering and search
- [ ] Export to CSV/Excel
- [ ] Email notifications
- [ ] Webhook integrations
- [ ] Advanced analytics dashboard
- [ ] Multi-language support
- [ ] Mobile app

---

**Note**: This tool is for legitimate lead generation purposes only. Please comply with all applicable laws and platform terms of service when using this software.