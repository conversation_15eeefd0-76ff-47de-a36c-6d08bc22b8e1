#!/usr/bin/env python3
"""
Main application entry point for the Social Media Lead Generation Bot.

This file sets up the FastAPI application with proper database initialization,
Dramatiq worker configuration, and all necessary dependencies.
"""

import uvicorn
import logging
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.api import app
from app.database import init_db, test_db_connection
from app import tasks  # Import to register Dramatiq actors
from config.settings import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def check_environment():
    """Check if all required environment variables are set."""
    missing_settings = Config.validate_required_settings()

    if missing_settings:
        logger.error(f"Missing required settings: {', '.join(missing_settings)}")
        logger.error("Please check your .env file and ensure all required variables are set.")
        Config.print_config_summary()
        return False

    return True

def initialize_application():
    """Initialize the application with database and other dependencies."""
    logger.info("Initializing Social Media Lead Generation Bot...")
    
    # Check environment variables
    if not check_environment():
        logger.error("Environment check failed. Exiting.")
        sys.exit(1)
    
    # Test database connection
    logger.info("Testing database connection...")
    if not test_db_connection():
        logger.error("Database connection failed. Please check your PostgreSQL configuration.")
        logger.error("Make sure PostgreSQL is running and the connection parameters are correct.")
        sys.exit(1)
    
    # Initialize database tables
    logger.info("Initializing database tables...")
    try:
        init_db()
        logger.info("Database tables initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database tables: {e}")
        sys.exit(1)
    
    logger.info("Application initialized successfully")

def run_api_server():
    """Run the FastAPI server."""
    initialize_application()

    host = Config.app.API_HOST
    port = Config.app.API_PORT

    logger.info(f"Starting FastAPI server on {host}:{port}")

    uvicorn.run(
        app,
        host=host,
        port=port,
        reload=Config.app.DEBUG,
        log_level=Config.app.LOG_LEVEL.lower()
    )

def run_dramatiq_worker():
    """Run the Dramatiq worker for background tasks."""
    initialize_application()
    
    logger.info("Starting Dramatiq worker...")
    
    # Import dramatiq CLI and run worker
    import dramatiq.cli
    import sys
    
    # Set up dramatiq worker arguments
    sys.argv = [
        'dramatiq',
        'app.tasks',
        '--processes', str(Config.app.DRAMATIQ_PROCESSES),
        '--threads', str(Config.app.DRAMATIQ_THREADS),
       # '--watch', str(project_root / 'app')
    ]
    
    dramatiq.cli.main()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Social Media Lead Generation Bot')
    parser.add_argument(
        'mode',
        choices=['api', 'worker', 'both'],
        help='Run mode: api (FastAPI server), worker (Dramatiq worker), or both'
    )
    
    args = parser.parse_args()
    
    if args.mode == 'api':
        run_api_server()
    elif args.mode == 'worker':
        run_dramatiq_worker()
    elif args.mode == 'both':
        import multiprocessing
        import time
        
        # Start API server in a separate process
        api_process = multiprocessing.Process(target=run_api_server)
        api_process.start()
        
        # Give API server time to start
        time.sleep(2)
        
        # Start Dramatiq worker in a separate process
        worker_process = multiprocessing.Process(target=run_dramatiq_worker)
        worker_process.start()
        
        try:
            # Wait for both processes
            api_process.join()
            worker_process.join()
        except KeyboardInterrupt:
            logger.info("Shutting down...")
            api_process.terminate()
            worker_process.terminate()
            api_process.join()
            worker_process.join()
