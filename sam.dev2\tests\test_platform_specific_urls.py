#!/usr/bin/env python3
"""
Test script to demonstrate platform-specific competitor URLs functionality.
This script shows how the updated system works with different competitor URLs for each platform.
"""

import json
import requests
from datetime import datetime

# Example API request with platform-specific competitor U<PERSON>s
def create_test_job():
    """Create a test scraping job with platform-specific competitor URLs."""
    
    # Test data with different competitor URLs for each platform
    test_request = {
        "user_id": "test_user_123",
        "business_domain": "Healthcare Technology",
        "business_goals": "Lead generation for B2B SaaS platform",
        "target_market": "Healthcare professionals and clinic owners",
        "business_website_url": "https://example-healthcare.com",
        "competitor_url": "https://generic-competitor.com",  # Fallback URL
        
        # Platform-specific competitor URLs (only for direct API platforms)
        "instagram_competitor_url": "https://www.instagram.com/healthcare_competitor/",
        "facebook_competitor_url": "https://www.facebook.com/healthcarecompetitor",
        "linkedin_competitor_url": "https://www.linkedin.com/company/healthcare-competitor/",
        "tiktok_competitor_url": "https://www.tiktok.com/@healthcare_competitor",
        
        "target_platforms": ["instagram", "facebook", "linkedin", "tiktok"],
        "leads_per_platform": {
            "instagram": 50,
            "facebook": 75,
            "linkedin": 100,
            "tiktok": 25
        },
        "additional_criteria": "Healthcare professionals aged 25-45, decision makers"
    }
    
    print("=== Test Request with Platform-Specific Competitor URLs ===")
    print(json.dumps(test_request, indent=2))
    print("\n" + "="*60 + "\n")
    
    # Simulate what the scraping_criteria would look like for each platform
    print("=== Expected Scraping Criteria for Each Platform ===")
    
    for platform in test_request["target_platforms"]:
        scraping_criteria = {
            'business_domain': test_request['business_domain'],
            'business_goals': test_request['business_goals'],
            'target_market': test_request['target_market'],
            'website_url': test_request['business_website_url'],
            'competitor_url': test_request['competitor_url'],
            'additional_criteria': test_request['additional_criteria']
        }
        
        # Add platform-specific competitor URL
        platform_url_key = f"{platform}_competitor_url"
        if platform_url_key in test_request:
            scraping_criteria[f'{platform}_competitor_url'] = test_request[platform_url_key]
        
        print(f"\n--- {platform.upper()} ---")
        print(f"Platform-specific URL key: {platform}_competitor_url")
        print(f"Platform-specific URL: {test_request.get(platform_url_key, 'Not provided')}")
        print(f"Final URL that will be used: {scraping_criteria.get(f'{platform}_competitor_url', scraping_criteria.get('competitor_url', scraping_criteria.get('website_url')))}")
        print(f"All scraping criteria keys: {list(scraping_criteria.keys())}")
    
    print("\n" + "="*60 + "\n")
    
    # Show how the process_direct_platform function would handle this
    print("=== How process_direct_platform Function Handles This ===")
    
    for platform in test_request["target_platforms"]:
        print(f"\n--- Processing {platform.upper()} ---")
        
        # Simulate the logic from process_direct_platform
        platform_key = platform.lower()
        platform_competitor_url_key = f"{platform_key}_competitor_url"
        
        # This is what the function would do:
        scraping_criteria = {
            'business_domain': test_request['business_domain'],
            'business_goals': test_request['business_goals'],
            'target_market': test_request['target_market'],
            'website_url': test_request['business_website_url'],
            'competitor_url': test_request['competitor_url'],
            'additional_criteria': test_request['additional_criteria']
        }
        
        # Add platform-specific URL
        if f"{platform}_competitor_url" in test_request:
            scraping_criteria[f'{platform}_competitor_url'] = test_request[f"{platform}_competitor_url"]
        
        # Get platform-specific competitor URL
        company_url = scraping_criteria.get(platform_competitor_url_key)
        
        # Fallback logic
        if not company_url:
            company_url = scraping_criteria.get('competitor_url') or scraping_criteria.get('website_url')
        
        print(f"1. Looking for platform-specific URL: {platform_competitor_url_key}")
        print(f"2. Found URL: {company_url}")
        print(f"3. Will use this URL for scraping: {company_url}")
        print(f"4. Platform: {platform} -> Collector: {platform.capitalize()}Collector")
    
    print("\n" + "="*60 + "\n")
    print("✅ Test completed! The system now supports platform-specific competitor URLs.")
    print("Each direct API platform (Instagram, Facebook, LinkedIn, TikTok) can have its own competitor URL.")
    print("Telegram and WhatsApp use a different processing flow and don't support platform-specific competitor URLs.")
    print("Fallback to generic competitor_url or website_url is available for all platforms.")

if __name__ == "__main__":
    create_test_job() 