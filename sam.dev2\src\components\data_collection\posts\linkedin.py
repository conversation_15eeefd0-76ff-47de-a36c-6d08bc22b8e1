import re
import os
import sys
import requests
from dotenv import load_dotenv
from collections import defaultdict
from rich import print


from .base_collector import BaseSocialMediaCollector
from config.settings import Config




class LinkedInCollector(BaseSocialMediaCollector):
    def __init__(self):
        self.rapidapi_key = Config.api_keys.LINKEDIN_RAPID_API_KEY
        self.headers = {
            "x-rapidapi-key": self.rapidapi_key,
            "x-rapidapi-host": "fresh-linkedin-scraper-api.p.rapidapi.com"
        }

    def extract_post_id_from_url(self, post_url: str) -> str:
        """
        Extract the post ID from a LinkedIn post URL.
        Example: https://www.linkedin.com/feed/update/urn:li:activity:7244804629786419202/
        """
        match = re.search(r'activity[:\-](\d+)', post_url)
        return match.group(1) if match else None

    def get_post_details(self, post_url: str) -> dict:
        """
        Get details for a LinkedIn post using the fresh-linkedin-scraper-api.

        Args:
            post_url: URL of the LinkedIn post

        Returns:
            Dictionary with post details or error message
        """
        post_id = self.extract_post_id_from_url(post_url)
        if not post_id:
            print(f"Could not extract post ID from URL: {post_url}")
            return {}

        api_url = "https://fresh-linkedin-scraper-api.p.rapidapi.com/api/v1/post/detail"
        params = {"post_id": post_id}

        try:
            response = requests.get(api_url, headers=self.headers, params=params, timeout=15)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"API request failed with status code: {response.status_code}")
                if hasattr(response, 'text'):
                    print(f"Response: {response.text}")
                return {}
        except Exception as e:
            print(f"Error fetching LinkedIn post details: {e}")
            return {}


    def extract_company_id_from_url(self, url: str) -> str:
        """
        Extract the company ID from a LinkedIn company URL.
        Example: https://www.linkedin.com/company/10649600/
        """
        match = re.search(r'linkedin\.com/company/(\d+)', url)
        return match.group(1) if match else url  # If not a URL, assume it's the ID
    

    def get_company_posts(self, url: str, count: int = 20):
        """
        Get all post URLs from a LinkedIn company page using the fresh-linkedin-scraper-api.

        Args:
            url: LinkedIn company URL or company ID
            page: Page number for pagination (default 1)

        Returns:
            A list of post URLs
        """
        try:
            company_id = self.extract_company_id_from_url(url)
            api_url = "https://fresh-linkedin-scraper-api.p.rapidapi.com/api/v1/company/posts"
            params = {"company_id": company_id, "page": str(page)}

            print(f"Fetching posts for LinkedIn company: {company_id}")
            response = requests.get(api_url, headers=self.headers, params=params, timeout=15)

            if response.status_code == 200:
                data = response.json()
                posts = data.get("data", [])
                post_urls = []
                for post in posts:
                    # Try to extract the post URL from the response
                    if isinstance(post, dict):
                        url = post.get("postUrl") or post.get("url")
                        if url:
                            post_urls.append(url)
                return post_urls
            else:
                print(f"API request failed with status code: {response.status_code}")
                if hasattr(response, 'text'):
                    print(f"Response: {response.text}")
                return []
        except Exception as e:
            print(f"Error getting company posts: {e}")
            return []
          


    def get_profile_posts(self, url: str, page: int = 1):
        """
        Get all post URLs from a LinkedIn user profile using the fresh-linkedin-scraper-api.

        Args:
            url: LinkedIn profile URL or username
            page: Page number for pagination (default 1)

        Returns:
            A list of post URLs
        """
        try:
            # Step 1: Extract username if a URL is provided
            username = url
            if "linkedin.com" in url and "/in/" in url:
                parts = url.split("/in/")
                if len(parts) > 1:
                    username = parts[1].split("?")[0].split("/")[0]
                    print(f"Extracted username from URL: {username}")

            # Step 2: Get URN from username
            profile_api = "https://fresh-linkedin-scraper-api.p.rapidapi.com/api/v1/user/profile"
            profile_params = {"username": username}
            profile_resp = requests.get(profile_api, headers=self.headers, params=profile_params, timeout=15)
            if profile_resp.status_code != 200:
                print(f"Failed to fetch profile for username '{username}'. Status: {profile_resp.status_code}")
                return []
            profile_data = profile_resp.json()
            urn = profile_data.get("data", {}).get("urn")
            if not urn:
                print(f"URN not found for username '{username}'.")
                return []

            # Step 3: Get posts using URN
            posts_api = "https://fresh-linkedin-scraper-api.p.rapidapi.com/api/v1/user/posts"
            posts_params = {"urn": urn, "page": str(page)}
            print(f"Fetching posts for LinkedIn user URN: {urn}")
            posts_resp = requests.get(posts_api, headers=self.headers, params=posts_params, timeout=15)
            if posts_resp.status_code != 200:
                print(f"Failed to fetch posts for URN '{urn}'. Status: {posts_resp.status_code}")
                return []
            posts_data = posts_resp.json()
            posts = posts_data.get("data", [])
            post_urls = []
            for post in posts:
                if isinstance(post, dict):
                    url = post.get("postUrl") or post.get("url")
                    if url:
                        post_urls.append(url)
            return post_urls

        except Exception as e:
            print(f"Error getting profile posts: {e}")
            return []


if __name__ == "__main__":
    collector = LinkedInCollector()

    # Test LinkedIn profile posts
    test_profiles = [
        "https://www.linkedin.com/in/adamselipsky",  # Profile URL
        "adamselipsky"  # Username only
    ]

    for profile in test_profiles:
        print(f"\nGetting post URLs for LinkedIn user: {profile}")
        posts = collector.get_profile_posts(profile)
        if posts:
            print(f"Found {len(posts)} posts:")
            for i, url in enumerate(posts, 1):
                print(f"  {i}. {url}")
        else:
            print("No posts found or an error occurred.")

    # Test LinkedIn company posts
    test_companies = [
        "https://www.linkedin.com/company/10649600/",  # Company ID URL
        "10649600"  # Company ID only
    ]

    for company in test_companies:
        print(f"\nGetting post URLs for LinkedIn company: {company}")
        posts = collector.get_company_posts(company)
        if posts:
            print(f"Found {len(posts)} posts:")
            for i, url in enumerate(posts, 1):
                print(f"  {i}. {url}")
        else:
            print("No posts found or an error occurred.")

        # Test LinkedIn post details
    test_post_urls = [
        # Use a valid LinkedIn post URL from your previous output
        "https://www.linkedin.com/feed/update/urn:li:activity:7244804629786419202"
    ]

    for post_url in test_post_urls:
        print(f"\nGetting details for LinkedIn post: {post_url}")
        details = collector.get_post_details(post_url)
        if details:
            print("Post details:")
            print(details)
        else:
            print("No details found or an error occurred.")