import re
import os
import sys
import requests
from dotenv import load_dotenv
from typing import List

# allow absolute imports from your project root
from .base_collector import BaseSocialMediaCollector
from config.settings import Config



class TikTokCollector(BaseSocialMediaCollector):
    def __init__(self):
        self.api_key = Config.api_keys.TIKTOK_RAPID_API_KEY
        self.api_host = Config.api_keys.TIKTOK_RAPID_API_HOST
        self.base_url = f"https://{self.api_host}"
        self.headers = {
            "x-rapidapi-key": self.api_key,
            "x-rapidapi-host": self.api_host,
        }

    def _make_request(self, path: str, params: dict) -> dict:
        url = f"{self.base_url}{path}"
        try:
            response = requests.get(url, headers=self.headers, params=params, timeout=15)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error making request to TikTok API: {e}")
            return {}

    def get_secuid_from_username(self, username: str) -> str:
        path = "/api/user/info"
        params = {"uniqueId": username}
        data = self._make_request(path, params)
        try:
            return data["userInfo"]["user"]["secUid"]
        except Exception as e:
            print(f"Could not extract secUid for {username}: {e}")
            return ""

    def get_profile_posts(self, url: str, count: int = 20) -> List[str]:
        username = url
        if "tiktok.com" in url:
            try:
                username = self.extract_username_from_url(url)
            except ValueError as e:
                # Try a simpler approach - extract the part after tiktok.com/
                parts = url.split('tiktok.com/')
                if len(parts) > 1:
                    potential_username = parts[1]
                    if potential_username.startswith('@'):
                        potential_username = potential_username[1:]
                    if '/' in potential_username:
                        potential_username = potential_username.split('/')[0]
                    if potential_username:
                        username = potential_username

        secuid = self.get_secuid_from_username(username)
        if not secuid:
            print(f"Could not get secUid for username: {username}")
            return []

        path = "/api/user/posts"
        params = {"secUid": secuid, "count": str(count), "cursor": "0"}
        data = self._make_request(path, params)
        item_list = []
        if "data" in data and "itemList" in data["data"]:
            item_list = data["data"]["itemList"]
        post_urls = []
        for post in item_list:
            video_id = (
                post.get("id")
                or post.get("video_id")
                or post.get("aweme_id")
                or post.get("item_id")
                or post.get("videoId")
            )
            if not video_id:
                video_id = post.get("idStr")
            if video_id:
                post_url = self.construct_post_url(username, video_id)
                post_urls.append(post_url)
        return post_urls

    def get_company_posts(self, url: str, count: int = 20) -> List[str]:
        return self.get_profile_posts(url, count)

    def extract_username_from_url(self, url: str) -> str:
        m = re.search(r'tiktok\.com/@([^/]+)', url)
        if not m:
            raise ValueError(f"Could not extract username from URL: {url}")
        return m.group(1)

    def extract_video_id_from_url(self, url: str) -> str:
        m = re.search(r'/video/(\d+)', url)
        if not m:
            raise ValueError(f"Could not extract video ID from URL: {url}")
        return m.group(1)

    def construct_post_url(self, username: str, video_id: str) -> str:
        return f"https://www.tiktok.com/@{username}/video/{video_id}"

    def get_post_details(self, post_url: str) -> dict:
        video_id = self.extract_video_id_from_url(post_url)
        path = "/api/post/detail"
        params = {"videoId": video_id}
        data = self._make_request(path, params)
        return data if data else {"id": video_id, "url": post_url}


# === Example Usage ===
if __name__ == "__main__":
    collector = TikTokCollector()

    # Get post URLs for a TikTok user (can use username or URL)
    url = "https://www.tiktok.com/@taylorswift"
    print(f"Getting post URLs for {url}...")
    posts = collector.get_profile_posts(url, count=5)

    if posts:
        print(f"\nPosts for {url}:")
        for i, post in enumerate(posts, 1):
            print(f"{i}. {post}")
        # Example: get details for the first post
        print("\nGetting details for first post:")
        details = collector.get_post_details(posts[0])
        print(details)
    else:
        print("No posts found.")