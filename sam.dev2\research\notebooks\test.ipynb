{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["key =\"**********************************************\""]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sam\n"]}], "source": ["print(\"sam\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting apify_client\n", "  Downloading apify_client-1.10.0-py3-none-any.whl.metadata (17 kB)\n", "Collecting apify-shared>=1.4.1 (from apify_client)\n", "  Downloading apify_shared-1.4.1-py3-none-any.whl.metadata (2.5 kB)\n", "Collecting httpx>=0.25 (from apify_client)\n", "  Using cached httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting more-itertools>=10.0.0 (from apify_client)\n", "  Downloading more_itertools-10.7.0-py3-none-any.whl.metadata (37 kB)\n", "Collecting anyio (from httpx>=0.25->apify_client)\n", "  Using cached anyio-4.9.0-py3-none-any.whl.metadata (4.7 kB)\n", "Requirement already satisfied: certifi in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from httpx>=0.25->apify_client) (2025.4.26)\n", "Collecting httpcore==1.* (from httpx>=0.25->apify_client)\n", "  Downloading httpcore-1.0.9-py3-none-any.whl.metadata (21 kB)\n", "Requirement already satisfied: idna in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from httpx>=0.25->apify_client) (3.10)\n", "Collecting h11>=0.16 (from httpcore==1.*->httpx>=0.25->apify_client)\n", "  Downloading h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)\n", "Requirement already satisfied: exceptiongroup>=1.0.2 in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from anyio->httpx>=0.25->apify_client) (1.3.0)\n", "Collecting sniffio>=1.1 (from anyio->httpx>=0.25->apify_client)\n", "  Using cached sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)\n", "Requirement already satisfied: typing_extensions>=4.5 in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from anyio->httpx>=0.25->apify_client) (4.13.2)\n", "Downloading apify_client-1.10.0-py3-none-any.whl (77 kB)\n", "Downloading apify_shared-1.4.1-py3-none-any.whl (12 kB)\n", "Using cached httpx-0.28.1-py3-none-any.whl (73 kB)\n", "Downloading httpcore-1.0.9-py3-none-any.whl (78 kB)\n", "Downloading h11-0.16.0-py3-none-any.whl (37 kB)\n", "Downloading more_itertools-10.7.0-py3-none-any.whl (65 kB)\n", "Using cached anyio-4.9.0-py3-none-any.whl (100 kB)\n", "Using cached sniffio-1.3.1-py3-none-any.whl (10 kB)\n", "Installing collected packages: sniffio, more-itertools, h11, apify-shared, httpcore, anyio, httpx, apify_client\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m8/8\u001b[0m [apify_client][0m [anyio]\n", "\u001b[1A\u001b[2KSuccessfully installed anyio-4.9.0 apify-shared-1.4.1 apify_client-1.10.0 h11-0.16.0 httpcore-1.0.9 httpx-0.28.1 more-itertools-10.7.0 sniffio-1.3.1\n"]}], "source": ["!pip install apify_client"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "ApifyApiError", "evalue": "You must rent a paid Actor in order to run it after its free trial has expired. To rent this Actor, go to https://console.apify.com/actors/fFEdgN51AXsXPQpXl", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mApifyApiError\u001b[0m                             Traceback (most recent call last)", "Cell \u001b[0;32mIn[5], line 15\u001b[0m\n\u001b[1;32m      7\u001b[0m run_input \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m      8\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mauthToken\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m      9\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mscrapeGroup\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttps://t.me/BitSquad\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     10\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstartPage\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;241m1\u001b[39m,\n\u001b[1;32m     11\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcount\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;241m10\u001b[39m,\n\u001b[1;32m     12\u001b[0m }\n\u001b[1;32m     14\u001b[0m \u001b[38;5;66;03m# Run the Actor and wait for it to finish\u001b[39;00m\n\u001b[0;32m---> 15\u001b[0m run \u001b[38;5;241m=\u001b[39m \u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mactor\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfFEdgN51AXsXPQpXl\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcall\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrun_input\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrun_input\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     17\u001b[0m \u001b[38;5;66;03m# Fetch and print Actor results from the run's dataset (if there are any)\u001b[39;00m\n\u001b[1;32m     18\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m item \u001b[38;5;129;01min\u001b[39;00m client\u001b[38;5;241m.\u001b[39mdataset(run[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdefaultDatasetId\u001b[39m\u001b[38;5;124m\"\u001b[39m])\u001b[38;5;241m.\u001b[39miterate_items():\n", "File \u001b[0;32m/opt/anaconda3/envs/dgen/lib/python3.10/site-packages/apify_client/_logging.py:82\u001b[0m, in \u001b[0;36m_injects_client_details_to_log_context.<locals>.wrapper\u001b[0;34m(resource_client, *args, **kwargs)\u001b[0m\n\u001b[1;32m     79\u001b[0m log_context\u001b[38;5;241m.\u001b[39mclient_method\u001b[38;5;241m.\u001b[39mset(fun\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__qualname__\u001b[39m)\n\u001b[1;32m     80\u001b[0m log_context\u001b[38;5;241m.\u001b[39mresource_id\u001b[38;5;241m.\u001b[39mset(resource_client\u001b[38;5;241m.\u001b[39mresource_id)\n\u001b[0;32m---> 82\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresource_client\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/anaconda3/envs/dgen/lib/python3.10/site-packages/apify_client/clients/resource_clients/actor.py:320\u001b[0m, in \u001b[0;36mActorClient.call\u001b[0;34m(self, run_input, content_type, build, max_items, max_total_charge_usd, memory_mbytes, timeout_secs, webhooks, wait_secs)\u001b[0m\n\u001b[1;32m    280\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mcall\u001b[39m(\n\u001b[1;32m    281\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m    282\u001b[0m     \u001b[38;5;241m*\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    291\u001b[0m     wait_secs: \u001b[38;5;28mint\u001b[39m \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m,\n\u001b[1;32m    292\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mdict\u001b[39m \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    293\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Start the Actor and wait for it to finish before returning the Run object.\u001b[39;00m\n\u001b[1;32m    294\u001b[0m \n\u001b[1;32m    295\u001b[0m \u001b[38;5;124;03m    It waits indefinitely, unless the wait_secs argument is provided.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    318\u001b[0m \u001b[38;5;124;03m        The run object.\u001b[39;00m\n\u001b[1;32m    319\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 320\u001b[0m     started_run \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstart\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    321\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrun_input\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrun_input\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    322\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcontent_type\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcontent_type\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    323\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbuild\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbuild\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    324\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmax_items\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmax_items\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    325\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmax_total_charge_usd\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmax_total_charge_usd\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    326\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmemory_mbytes\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmemory_mbytes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    327\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout_secs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout_secs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    328\u001b[0m \u001b[43m        \u001b[49m\u001b[43mwebhooks\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mwebhooks\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    329\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    331\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mroot_client\u001b[38;5;241m.\u001b[39mrun(started_run[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mid\u001b[39m\u001b[38;5;124m'\u001b[39m])\u001b[38;5;241m.\u001b[39mwait_for_finish(wait_secs\u001b[38;5;241m=\u001b[39mwait_secs)\n", "File \u001b[0;32m/opt/anaconda3/envs/dgen/lib/python3.10/site-packages/apify_client/_logging.py:82\u001b[0m, in \u001b[0;36m_injects_client_details_to_log_context.<locals>.wrapper\u001b[0;34m(resource_client, *args, **kwargs)\u001b[0m\n\u001b[1;32m     79\u001b[0m log_context\u001b[38;5;241m.\u001b[39mclient_method\u001b[38;5;241m.\u001b[39mset(fun\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__qualname__\u001b[39m)\n\u001b[1;32m     80\u001b[0m log_context\u001b[38;5;241m.\u001b[39mresource_id\u001b[38;5;241m.\u001b[39mset(resource_client\u001b[38;5;241m.\u001b[39mresource_id)\n\u001b[0;32m---> 82\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresource_client\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/anaconda3/envs/dgen/lib/python3.10/site-packages/apify_client/clients/resource_clients/actor.py:270\u001b[0m, in \u001b[0;36mActorClient.start\u001b[0;34m(self, run_input, content_type, build, max_items, max_total_charge_usd, memory_mbytes, timeout_secs, wait_for_finish, webhooks)\u001b[0m\n\u001b[1;32m    258\u001b[0m run_input, content_type \u001b[38;5;241m=\u001b[39m encode_key_value_store_record_value(run_input, content_type)\n\u001b[1;32m    260\u001b[0m request_params \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_params(\n\u001b[1;32m    261\u001b[0m     build\u001b[38;5;241m=\u001b[39mbuild,\n\u001b[1;32m    262\u001b[0m     maxItems\u001b[38;5;241m=\u001b[39mmax_items,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    267\u001b[0m     webhooks\u001b[38;5;241m=\u001b[39mencode_webhook_list_to_base64(webhooks) \u001b[38;5;28;01mif\u001b[39;00m webhooks \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    268\u001b[0m )\n\u001b[0;32m--> 270\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhttp_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcall\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    271\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_url\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mruns\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    272\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mPOST\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    273\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m{\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mcontent-type\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mcontent_type\u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    274\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrun_input\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    275\u001b[0m \u001b[43m    \u001b[49m\u001b[43mparams\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest_params\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    276\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    278\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m parse_date_fields(pluck_data(response\u001b[38;5;241m.\u001b[39mjson()))\n", "File \u001b[0;32m/opt/anaconda3/envs/dgen/lib/python3.10/site-packages/apify_client/_http_client.py:218\u001b[0m, in \u001b[0;36mHTTPClient.call\u001b[0;34m(self, method, url, headers, params, data, json, stream, parse_response, timeout_secs)\u001b[0m\n\u001b[1;32m    215\u001b[0m         stop_retrying()\n\u001b[1;32m    216\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ApifyApiError(response, attempt)\n\u001b[0;32m--> 218\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mretry_with_exp_backoff\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    219\u001b[0m \u001b[43m    \u001b[49m\u001b[43m_make_request\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    220\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmax_retries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    221\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbackoff_base_millis\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmin_delay_between_retries_millis\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    222\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbackoff_factor\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mDEFAULT_BACKOFF_EXPONENTIAL_FACTOR\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    223\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrandom_factor\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mDEFAULT_BACKOFF_RANDOM_FACTOR\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    224\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/anaconda3/envs/dgen/lib/python3.10/site-packages/apify_client/_utils.py:66\u001b[0m, in \u001b[0;36mretry_with_exp_backoff\u001b[0;34m(func, max_retries, backoff_base_millis, backoff_factor, random_factor)\u001b[0m\n\u001b[1;32m     64\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m attempt \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;241m1\u001b[39m, max_retries \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m):\n\u001b[1;32m     65\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 66\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstop_retrying\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mattempt\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     67\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[1;32m     68\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m swallow:\n", "File \u001b[0;32m/opt/anaconda3/envs/dgen/lib/python3.10/site-packages/apify_client/_http_client.py:216\u001b[0m, in \u001b[0;36mHTTPClient.call.<locals>._make_request\u001b[0;34m(stop_retrying, attempt)\u001b[0m\n\u001b[1;32m    214\u001b[0m     logger\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mStatus code is not retryable\u001b[39m\u001b[38;5;124m'\u001b[39m, extra\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstatus_code\u001b[39m\u001b[38;5;124m'\u001b[39m: response\u001b[38;5;241m.\u001b[39mstatus_code})\n\u001b[1;32m    215\u001b[0m     stop_retrying()\n\u001b[0;32m--> 216\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m ApifyApiError(response, attempt)\n", "\u001b[0;31mApifyApiError\u001b[0m: You must rent a paid Actor in order to run it after its free trial has expired. To rent this Actor, go to https://console.apify.com/actors/fFEdgN51AXsXPQpXl"]}], "source": ["from apify_client import ApifyClient\n", "\n", "def scrape_telegram_group(api_key: str, group_url: str, start_page: int = 1, count: int = 10):\n", "    \"\"\"\n", "    Scrape a Telegram group using Apify's Telegram scraper.\n", "    \n", "    Args:\n", "        api_key (str): Apify API key\n", "        group_url (str): URL of the Telegram group to scrape\n", "        start_page (int): Starting page number\n", "        count (int): Number of items to scrape\n", "        \n", "    Returns:\n", "        list: Scraped items from the group\n", "    \"\"\"\n", "    # Initialize the ApifyClient with API token\n", "    client = ApifyClient(api_key)\n", "    \n", "    # Configure the scraper input\n", "    run_input = {\n", "        \"authToken\": None,\n", "        \"scrapeGroup\": group_url,\n", "        \"startPage\": start_page,\n", "        \"count\": count,\n", "    }\n", "    \n", "    # Execute the scraper\n", "    run = client.actor(\"fFEdgN51AXsXPQpXl\").call(run_input=run_input)\n", "    \n", "    # Collect and return results\n", "    results = []\n", "    for item in client.dataset(run[\"defaultDatasetId\"]).iterate_items():\n", "        results.append(item)\n", "        print(item)\n", "    \n", "    return results\n", "\n", "# Example usage\n", "if __name__ == \"__main__\":\n", "    results = scrape_telegram_group(\n", "        api_key=key,\n", "        group_url=\"https://t.me/BitSquad\",\n", "        start_page=1,\n", "        count=10\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Web search Agent"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["OPENAI_API_KEY = \"*****************************************************************************************************************************************************************\"\n", "TAVILY_API_KEY = \"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\"\n", "GROQ_API_KEY = \"********************************************************\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting tavily-python\n", "  Downloading tavily_python-0.7.2-py3-none-any.whl.metadata (7.0 kB)\n", "Requirement already satisfied: requests in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from tavily-python) (2.32.3)\n", "Collecting tiktoken>=0.5.1 (from tavily-python)\n", "  Using cached tiktoken-0.9.0-cp310-cp310-macosx_11_0_arm64.whl.metadata (6.7 kB)\n", "Requirement already satisfied: httpx in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from tavily-python) (0.28.1)\n", "Collecting regex>=2022.1.18 (from tiktoken>=0.5.1->tavily-python)\n", "  Using cached regex-2024.11.6-cp310-cp310-macosx_11_0_arm64.whl.metadata (40 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from requests->tavily-python) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from requests->tavily-python) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from requests->tavily-python) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from requests->tavily-python) (2025.4.26)\n", "Requirement already satisfied: anyio in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from httpx->tavily-python) (4.9.0)\n", "Requirement already satisfied: httpcore==1.* in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from httpx->tavily-python) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from httpcore==1.*->httpx->tavily-python) (0.16.0)\n", "Requirement already satisfied: exceptiongroup>=1.0.2 in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from anyio->httpx->tavily-python) (1.3.0)\n", "Requirement already satisfied: sniffio>=1.1 in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from anyio->httpx->tavily-python) (1.3.1)\n", "Requirement already satisfied: typing_extensions>=4.5 in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from anyio->httpx->tavily-python) (4.13.2)\n", "Downloading tavily_python-0.7.2-py3-none-any.whl (14 kB)\n", "Using cached tiktoken-0.9.0-cp310-cp310-macosx_11_0_arm64.whl (1.0 MB)\n", "Using cached regex-2024.11.6-cp310-cp310-macosx_11_0_arm64.whl (284 kB)\n", "Installing collected packages: regex, tiktoken, tavily-python\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3/3\u001b[0m [tavily-python]\n", "\u001b[1A\u001b[2KSuccessfully installed regex-2024.11.6 tavily-python-0.7.2 tiktoken-0.9.0\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install tavily-python"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Open AI Key: *****************************************************************************************************************************************************************\n", "\n", "Final Results:\n", "{\n", "  \"url\": \"https://www.flutterwave.com\",\n", "  \"title\": \"Endless possibilities for every business - Flutterwave\",\n", "  \"meta_description\": \"Unlocking boundless payment opportunities for enterprises, individuals, small businesses, emerging markets, and startups alike.\",\n", "  \"text_content\": \"Enterprise\\n         \\n                  Payment solutions\\n                 \\n                  Grow your business\\n                 \\n              Payment solutions\\n             \\n              Grow your business\\n                  Accept online payments Easily receive online payments from around the world in different currencies.      Payout & Transfers Experience effortless cross-border payouts and transfers in various currencies worldwide.       \\n                      Swap\\n                     \\n                      Swap your Naira for Dollars, Euros and Pounds\\n                         POS systems Point of Sale devices with multiple payment methods to grow your physical business.        Checkout demo Experience checkout yourself. Accept online payments Easily receive online payments from around the world in different currencies. Payout & Transfers Experience effortless cross-border payouts and transfers in various currencies worldwide. Swap\\n                     \\n                      Swap your Naira for Dollars, Euros and Pounds POS systems Point of Sale devices with multiple payment methods to grow your physical business. Checkout demo Experience checkout yourself. SME\\n         \\n                  Sell to anyone anywhere\\n                 \\n                  Grow your business\\n                 \\n              Sell to anyone anywhere\\n             \\n              Grow your business\\n               Scan to download the mobile app.        \\n                        Online checkout\\n                       \\n                        Add our checkout to your Shopify, WordPress, WooCommerce, Joomla and PrestaShop stores.\\n                             \\n                        Payment Links \\n                        Accept payments without writing code.\\n                           \\n                        POS systems \\n                        Point of Sale devices with multiple payment methods to grow your physical business.\\n                            \\n                        Build your online store\\n                       \\n                        Create a free online store that helps you find customers, and accept payments globally.\\n                             \\n                        Swap\\n                       \\n                        Swap your Naira for Dollars, Euros and Pounds\\n                              \\n                        Invoices \\n                        Create and issue professional invoices for global payments.\\n                            \\n                        Disha for creators \\n                        Effortlessly create a free single-page website, sell products globally, no coding required.\\n                                \\n                        Afritickets \\n                        Experience the integrated offering of lifestyle, business, event, and travel management services.\\n                       Download the mobile app Online checkout\\n                       \\n                        Add our checkout to your Shopify, WordPress, WooCommerce, Joomla and PrestaShop stores. Payment Links \\n                        Accept payments without writing code. POS systems \\n                        Point of Sale devices with multiple payment methods to grow your physical business. Build your online store\\n                       \\n                        Create a free online store that helps you find customers, and accept payments globally. Swap\\n                       \\n                        Swap your Naira for Dollars, Euros and Pounds Invoices \\n                        Create and issue professional invoices for global payments. Disha for creators \\n                        Effortlessly create a free single-page website, sell products globally, no coding required. Afritickets \\n                        Experience the integrated offering of lifestyle, business, event, and travel management services. Personal\\n               \\n                    Send App\\n                   \\n                    Safely, quickly and conveniently send money to the people who matter most to you.\\n                        \\n                    Market \\n                    Discover, shop and get your items delivered to you from your favourite online businesses.\\n                         \\n                    Swap \\n                    Swap your Naira for Dollars, Euros and Pounds\\n                            \\n                    Buy event tickets \\n                    Conveniently find events you love and buy your tickets.\\n                         \\n                    Tuition \\n                    Quick and easy tuition fee payments to African and international schools.\\n                     Scan to download Send mobile app. \\n              Download Send mobile app Send App\\n                   \\n                    Safely, quickly and conveniently send money to the people who matter most to you. Market \\n                    Discover, shop and get your items delivered to you from your favourite online businesses. Swap \\n                    Swap your Naira for Dollars, Euros and Pounds Buy event tickets \\n                    Conveniently find events you love and buy your tickets. Tuition \\n                    Quick and easy tuition fee payments to African and international schools. Resources\\n         \\n                  Pricing \\n                  Blog \\n                  Customer case studies\\n                 \\n                  Integrations Pricing Blog Customer case studies Integrations Developers\\n         \\n                  API documentation \\n                  API reference \\n                  API status API documentation API reference API status Company\\n         \\n                  Careers \\n                  Press Careers Press Sign in Endless possibilities\\n          for all Unlocking boundless payment opportunities for enterprises, individuals, small businesses, emerging markets, and startups alike. Send money home to loved ones, sell online as a small business, process global payments as an enterprise, build financial products as a startup. With Flutterwave, the question isn't what's possible\\u2014it is: what isn't? We are trusted by\\n          1 million+\\n          businesses & 2 million+\\n          individuals Endless payment possibilities\\n        for enterprises Our online checkout features our smart payment ordering system, incredible speed, and beautiful simplicity. It's designed to grow your revenue and provide the best payment experience to your customers. Easily receive money from anyone, anywhere in the world via our multitude of secure payment methods. Make single or bulk transfers to bank accounts from your Flutterwave dashboard. Endless payment possibilities for every\\n        Small Business. Create a free ecommerce website and start selling worldwide with just a few clicks. Generate professional invoices for your customers and get paid from anywhere. Receive one-off or recurring payments from anyone, anywhere, via your unique payment link. Let\\u2019s play to our strengths, shall we? You focus on bringing your amazing ideas to life and we focus on providing all the\\n        commerce tools\\n        you need. Endless payment possibilities for every\\n        individual Flutterwave offers a host of seamless products for individuals, ensuring smooth transactions and efficient money management. Experience the convenience and security of Send, enabling you to effortlessly and securely transfer funds to your beloved ones residing overseas. Begin sharing financial support with the ones who hold significance in your life right\\n      here Explore a world of possibilities as you Discover, Shop, and have your purchases conveniently delivered from your cherished online businesses. Embark on a secure shopping journey and relish the joy of acquiring great items from amazing small businesses, right\\n      here Browse upcoming events and secure your ticket on our Afritickets platform\\n      here Well documented and easy-to-use\\n        APIs\\n        for\\n        developers We have done the core payments integrations and abstractions, so your team can easily integrate with our APIs and access multiple payment functionalities. Quick transfers Initiate one-time and recurring payments Payment verification Instant virtual cards creation Customer verification Our\\n        global \\n        reach We accept payments in more than thirty currencies. API calls per day, peaking at 231 requests per second. Average number of payments processed daily. Debit & Credit cards Bank Account Mobile money POS M-Pesa VISA QR Bank Transfer USSD Ready to get started? Create an account and instantly start accepting payments, selling your beautiful products online and building financial tools. We use cookies on our website to improve your user experience. You can\\n        accept or manage your cookie preferences. To learn more about our use\\n        of cookies,\\n        view our detailed Cookie Notice. Please be aware that Flutterwave will never ask for your card details, PIN, OTP/token codes, or any account-related information via call, SMS, WhatsApp or email. Please do not respond to such requests. Cameroon Ghana Ivory Coast Kenya Malawi Nigeria Rwanda Senegal South Africa Tanzania Uganda Zambia \\u00a9\\n        \\n          Flutterwave Payments, LLC is Registered with\\n          FinCEN. NMLS ID# 2391594\"\n", "}\n"]}], "source": ["import os\n", "import json\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from openai import OpenAI\n", "from tavily import TavilyClient\n", "import re\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "\n", "class CompanyTelegramPipeline:\n", "    def __init__(self, tavily_api_key=None, openai_api_key=None):\n", "        \"\"\"Initialize the pipeline with API keys.\"\"\"\n", "        self.tavily_api_key = tavily_api_key or os.environ.get(\"TAVILY_API_KEY\")\n", "        self.openai_api_key = openai_api_key or os.environ.get(\"OPENAI_API_KEY\")\n", "        print(f\"Open AI Key: {self.openai_api_key}\")\n", "        if not self.tavily_api_key:\n", "            raise ValueError(\"Tavily API key is required\")\n", "        if not self.openai_api_key:\n", "            raise ValueError(\"OpenAI API key is required\")\n", "        \n", "        self.tavily_client = TavilyClient(api_key=self.tavily_api_key)\n", "        self.openai_client = OpenAI(api_key=self.openai_api_key)\n", "    \n", "    def scrape_website_content(self, company_url):\n", "        \"\"\"Scrape raw website content for processing with OpenAI.\"\"\"\n", "        try:\n", "            headers = {\n", "                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n", "            }\n", "            response = requests.get(company_url, headers=headers, timeout=10)\n", "            response.raise_for_status()\n", "            \n", "            soup = BeautifulSoup(response.text, 'html.parser')\n", "            \n", "            # Extract title\n", "            title = \"\"\n", "            title_tag = soup.find('title')\n", "            if title_tag and title_tag.text:\n", "                title = title_tag.text.strip()\n", "            \n", "            # Extract meta description\n", "            meta_description = \"\"\n", "            meta_desc = soup.find('meta', attrs={'name': 'description'})\n", "            if not meta_desc:\n", "                meta_desc = soup.find('meta', attrs={'property': 'og:description'})\n", "            if meta_desc and meta_desc.get('content'):\n", "                meta_description = meta_desc.get('content').strip()\n", "            \n", "            # Extract text content from paragraphs and headers\n", "            text_content = []\n", "            for tag in soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'li']):\n", "                if tag.text.strip():\n", "                    text_content.append(tag.text.strip())\n", "            \n", "            # Combine content with priority to meta information\n", "            website_content = {\n", "                'url': company_url,\n", "                'title': title,\n", "                'meta_description': meta_description,\n", "                'text_content': ' '.join(text_content[:100])  # Limit text to avoid token limits\n", "            }\n", "            \n", "            return website_content\n", "            \n", "        except Exception as e:\n", "            print(f\"Error scraping {company_url}: {str(e)}\")\n", "            return None\n", "    \n", "    def extract_company_info_with_llm(self, website_content, company_goals=None):\n", "        \"\"\"Use OpenAI to extract structured company information from website content or goals.\"\"\"\n", "        if not website_content and not company_goals:\n", "            return None\n", "        \n", "        prompt = f\"\"\"\n", "        Extract key business information from this company information.\n", "        \n", "        {'Website Content:' if website_content else 'Company Goals:'}\n", "        {website_content['text_content'] if website_content else company_goals}\n", "        \n", "        Please extract and format the following information in JSON format:\n", "        \n", "        1. name: The company name\n", "        2. description: A 1-2 sentence description of what the company does\n", "        3. keywords: 5-10 keywords relevant to the company's business\n", "        4. industry: The primary industry or business sector\n", "        5. services: List of main products/services offered\n", "        6. location: Geographic focus or headquarters location if mentioned\n", "        7. target_audience: The company's target customers or users\n", "        8. country: The primary country of operation\n", "        \n", "        Format the response as valid JSON.\n", "        \"\"\"\n", "        \n", "        try:\n", "            response = self.openai_client.chat.completions.create(\n", "                model=\"gpt-4\",\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": \"You are an AI assistant specialized in extracting business information. Return only valid JSON.\"},\n", "                    {\"role\": \"user\", \"content\": prompt}\n", "                ],\n", "                response_format={\"type\": \"json_object\"}\n", "            )\n", "            \n", "            # Parse the JSON response\n", "            company_info = json.loads(response.choices[0].message.content)\n", "            return company_info\n", "            \n", "        except Exception as e:\n", "            print(f\"Error extracting company info with LLM: {str(e)}\")\n", "            return None\n", "    \n", "    def search_telegram_groups(self, company_info):\n", "        \"\"\"Search for telegram groups using Tavily API.\"\"\"\n", "        if not company_info:\n", "            return []\n", "        \n", "        # Construct search query using company info\n", "        query = f\"telegram groups {company_info.get('industry', '')} {company_info.get('services', [])} {company_info.get('country', '')}\"\n", "        \n", "        try:\n", "            # Search with <PERSON><PERSON>\n", "            search_results = self.tavily_client.search(query, max_results=4)\n", "            \n", "            telegram_groups = []\n", "            non_telegram_urls = []\n", "            \n", "            # Process results\n", "            for result in search_results.get('results', []):\n", "                url = result.get('url', '')\n", "                if 't.me/' in url or 'telegram.me/' in url:\n", "                    telegram_groups.append({\n", "                        'url': url,\n", "                        'title': result.get('title', ''),\n", "                        'description': result.get('content', '')\n", "                    })\n", "                else:\n", "                    non_telegram_urls.append(url)\n", "            \n", "            # Scrape non-telegram websites for telegram links\n", "            for url in non_telegram_urls:\n", "                try:\n", "                    headers = {\n", "                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n", "                    }\n", "                    response = requests.get(url, headers=headers, timeout=10)\n", "                    soup = BeautifulSoup(response.text, 'html.parser')\n", "                    \n", "                    # Find all links\n", "                    for link in soup.find_all('a', href=True):\n", "                        href = link['href']\n", "                        if 't.me/' in href or 'telegram.me/' in href:\n", "                            telegram_groups.append({\n", "                                'url': href,\n", "                                'title': link.text.strip() or 'Telegram Group',\n", "                                'description': 'Found on ' + url\n", "                            })\n", "                except Exception as e:\n", "                    print(f\"Error scraping {url} for telegram links: {str(e)}\")\n", "                    continue\n", "            \n", "            return telegram_groups\n", "            \n", "        except Exception as e:\n", "            print(f\"Error searching for telegram groups: {str(e)}\")\n", "            return []\n", "    \n", "    def run_pipeline(self, company_url=None, company_goals=None):\n", "        \"\"\"Run the complete pipeline.\"\"\"\n", "        if not company_url and not company_goals:\n", "            raise ValueError(\"Either company_url or company_goals must be provided\")\n", "        \n", "        # Step 1: Scrape website content if URL provided\n", "        website_content = None\n", "        if company_url:\n", "            website_content = self.scrape_website_content(company_url)\n", "        \n", "        # Step 2: Extract company info\n", "        company_info = self.extract_company_info_with_llm(website_content, company_goals)\n", "        \n", "        # Step 3: Search for telegram groups\n", "        telegram_groups = self.search_telegram_groups(company_info)\n", "        \n", "        return {\n", "            \"company_info\": company_info,\n", "            \"telegram_groups\": telegram_groups\n", "        }\n", "\n", "# Example usage\n", "if __name__ == \"__main__\":\n", "    # Set your API keys in environment variables or pass directly\n", "    pipeline = CompanyTelegramPipeline(\n", "        tavily_api_key=os.environ.get(\"TAVILY_API_KEY\"),\n", "        openai_api_key=os.environ.get(\"OPENAI_API_KEY\")\n", "    )\n", "    \n", "    # Example 1: Run with company URL\n", "    results = pipeline.scrape_website_content(\"https://www.flutterwave.com\")\n", "    print(\"\\nFinal Results:\")\n", "    print(json.dumps(results, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["\n", "class CompanyTelegramPipeline:\n", "    def __init__(self, tavily_api_key=None, openai_api_key=None):\n", "        \"\"\"Initialize the pipeline with API keys.\"\"\"\n", "        self.tavily_api_key = tavily_api_key or os.environ.get(\"TAVILY_API_KEY\")\n", "        self.openai_api_key = openai_api_key or os.environ.get(\"OPENAI_API_KEY\")\n", "        \n", "        if not self.tavily_api_key:\n", "            raise ValueError(\"Tavily API key is required\")\n", "        if not self.openai_api_key:\n", "            raise ValueError(\"OpenAI API key is required\")\n", "        \n", "        self.tavily_client = TavilyClient(api_key=self.tavily_api_key)\n", "        self.openai_client = OpenAI(api_key=self.openai_api_key)\n", "    \n", "    def scrape_website_content(self, company_url):\n", "        \"\"\"Scrape raw website content for processing with OpenAI.\"\"\"\n", "        try:\n", "            headers = {\n", "                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n", "            }\n", "            response = requests.get(company_url, headers=headers, timeout=10)\n", "            response.raise_for_status()\n", "            \n", "            soup = BeautifulSoup(response.text, 'html.parser')\n", "            \n", "            # Extract title\n", "            title = \"\"\n", "            title_tag = soup.find('title')\n", "            if title_tag and title_tag.text:\n", "                title = title_tag.text.strip()\n", "            \n", "            # Extract meta description\n", "            meta_description = \"\"\n", "            meta_desc = soup.find('meta', attrs={'name': 'description'})\n", "            if not meta_desc:\n", "                meta_desc = soup.find('meta', attrs={'property': 'og:description'})\n", "            if meta_desc and meta_desc.get('content'):\n", "                meta_description = meta_desc.get('content').strip()\n", "            \n", "            # Extract text content from paragraphs and headers\n", "            text_content = []\n", "            for tag in soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'li']):\n", "                if tag.text.strip():\n", "                    text_content.append(tag.text.strip())\n", "            \n", "            # Combine content with priority to meta information\n", "            website_content = {\n", "                'url': company_url,\n", "                'title': title,\n", "                'meta_description': meta_description,\n", "                'text_content': ' '.join(text_content[:100])  # Limit text to avoid token limits\n", "            }\n", "            \n", "            return website_content\n", "            \n", "        except Exception as e:\n", "            print(f\"Error scraping {company_url}: {str(e)}\")\n", "            return None\n", "    \n", "    def extract_company_info_with_llm(self, website_content):\n", "        \"\"\"Use OpenAI to extract structured company information from website content.\"\"\"\n", "        if not website_content:\n", "            return None\n", "        \n", "        prompt = f\"\"\"\n", "        Extract key business information from this company website content.\n", "        \n", "        URL: {website_content['url']}\n", "        Title: {website_content['title']}\n", "        Meta Description: {website_content['meta_description']}\n", "        \n", "        Content: {website_content['text_content']}\n", "        \n", "        Please extract and format the following information in JSON format:\n", "        \n", "        1. name: The company name\n", "        2. description: A 1-2 sentence description of what the company does\n", "        3. keywords: 5-10 keywords relevant to the company's business\n", "        4. industry: The primary industry or business sector\n", "        5. services: List of main products/services offered\n", "        6. location: Geographic focus or headquarters location if mentioned\n", "        7. target_audience: The company's target customers or users\n", "        \n", "        Format the response as valid JSON.\n", "        \"\"\"\n", "        \n", "        try:\n", "            response = self.openai_client.chat.completions.create(\n", "                model=\"gpt-4o-mini\",\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": \"You are an AI assistant specialized in extracting business information from website content. Return only valid JSON.\"},\n", "                    {\"role\": \"user\", \"content\": prompt}\n", "                ],\n", "                response_format={\"type\": \"json_object\"}\n", "            )\n", "            \n", "            # Parse the JSON response\n", "            company_info = json.loads(response.choices[0].message.content)\n", "            return company_info\n", "            \n", "        except Exception as e:\n", "            print(f\"Error extracting company info with LLM: {str(e)}\")\n", "            return None\n", "        \n", "# Example usage\n", "if __name__ == \"__main__\":\n", "    # Set your API keys in environment variables or pass directly\n", "    pipeline = CompanyTelegramPipeline(\n", "        tavily_api_key=os.environ.get(\"TAVILY_API_KEY\"),\n", "        openai_api_key=os.environ.get(\"OPENAI_API_KEY\")\n", "    )\n", "    \n", "    # Example 1: Run with company URL\n", "    results = pipeline.run_pipeline(\n", "        company_url=\"https://www.flutterwave.com\"\n", "    )\n", "    print(\"\\nFinal Results:\")\n", "    print(json.dumps(results[\"telegram_groups\"], indent=2))\n", "    \n", "    # Example 2: Run with company goals (when URL is not available)\n", "    results = pipeline.run_pipeline(\n", "        company_goals=\"We're building a digital wallet for cross-border payments in Africa, focusing on Nigeria, Kenya, and Ghana. We want to make it easy for people to send money to family members across borders.\"\n", "    )\n", "    print(\"\\nFinal Results for goals-based search:\")\n", "    print(json.dumps(results[\"telegram_groups\"], indent=2))try:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (3964869888.py, line 21)", "output_type": "error", "traceback": ["\u001b[0;36m  Cell \u001b[0;32mIn[9], line 21\u001b[0;36m\u001b[0m\n\u001b[0;31m    print(json.dumps(results[\"telegram_groups\"], indent=2))try:\u001b[0m\n\u001b[0m                                                           ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m invalid syntax\n"]}], "source": ["# Example usage\n", "if __name__ == \"__main__\":\n", "    # Set your API keys in environment variables or pass directly\n", "    pipeline = CompanyTelegramPipeline(\n", "        tavily_api_key=os.environ.get(\"TAVILY_API_KEY\"),\n", "        openai_api_key=os.environ.get(\"OPENAI_API_KEY\")\n", "    )\n", "    \n", "    # Example 1: Run with company URL\n", "    results = pipeline.run_pipeline(\n", "        company_url=\"https://www.flutterwave.com\"\n", "    )\n", "    print(\"\\nFinal Results:\")\n", "    print(json.dumps(results[\"telegram_groups\"], indent=2))\n", "    \n", "    # Example 2: Run with company goals (when URL is not available)\n", "    results = pipeline.run_pipeline(\n", "        company_goals=\"We're building a digital wallet for cross-border payments in Africa, focusing on Nigeria, Kenya, and Ghana. We want to make it easy for people to send money to family members across borders.\"\n", "    )\n", "    print(\"\\nFinal Results for goals-based search:\")\n", "    print(json.dumps(results[\"telegram_groups\"], indent=2))try:\n", "    \n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting lxml\n", "  Downloading lxml-5.4.0-cp310-cp310-macosx_10_9_universal2.whl.metadata (3.5 kB)\n", "Requirement already satisfied: requests in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (2.32.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from requests) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from requests) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from requests) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /opt/anaconda3/envs/dgen/lib/python3.10/site-packages (from requests) (2025.4.26)\n", "Downloading lxml-5.4.0-cp310-cp310-macosx_10_9_universal2.whl (8.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m8.1/8.1 MB\u001b[0m \u001b[31m9.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hInstalling collected packages: lxml\n", "Successfully installed lxml-5.4.0\n"]}], "source": ["!pip install lxml requests"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fetching Members...\n", "An error occurred: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Traceback (most recent call last):\n", "  File \"/var/folders/xp/jfsq68yd6md3w078h7gx7sn40000gn/T/ipykernel_11939/3761207508.py\", line 46, in scrape_telegram_members\n", "    all_participants = await client.get_participants(target, aggressive=True)\n", "  File \"/opt/anaconda3/envs/dgen/lib/python3.10/site-packages/telethon/client/chats.py\", line 488, in get_participants\n", "    return await self.iter_participants(*args, **kwargs).collect()\n", "  File \"/opt/anaconda3/envs/dgen/lib/python3.10/site-packages/telethon/requestiter.py\", line 113, in collect\n", "    async for message in self:\n", "  File \"/opt/anaconda3/envs/dgen/lib/python3.10/site-packages/telethon/requestiter.py\", line 74, in __anext__\n", "    if await self._load_next_chunk():\n", "  File \"/opt/anaconda3/envs/dgen/lib/python3.10/site-packages/telethon/client/chats.py\", line 210, in _load_next_chunk\n", "    participants = await self.client(self.requests)\n", "  File \"/opt/anaconda3/envs/dgen/lib/python3.10/site-packages/telethon/client/users.py\", line 30, in __call__\n", "    return await self._call(self._sender, request, ordered=ordered)\n", "  File \"/opt/anaconda3/envs/dgen/lib/python3.10/site-packages/telethon/client/users.py\", line 92, in _call\n", "    result = await future\n", "telethon.errors.rpcerrorlist.ChatAdminRequiredError: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)\n"]}], "source": ["# Fixed code for Google Colab - arranged by Solved4You 2.0\n", "from telethon import TelegramClient  # Remove .sync for async mode\n", "from telethon.tl.functions.messages import GetDialogsRequest\n", "from telethon.tl.types import InputPeerEmpty, InputPeerChannel, InputPeerUser\n", "from telethon.errors.rpcerrorlist import PeerFloodError, UserPrivacyRestrictedError, SessionPasswordNeededError\n", "from telethon.tl.functions.channels import InviteToChannelRequest\n", "import sys\n", "import csv\n", "import traceback\n", "import time\n", "import asyncio\n", "\n", "# Account details\n", "api_id = '********'\n", "api_hash = 'f9ab963bd50e9669e6ea7d7da4da1e0f'\n", "phone = '+*************'\n", "\n", "session_name = 'telegram_scrape'\n", "\n", "async def scrape_telegram_members():\n", "    # Create client\n", "    client = TelegramClient(str(session_name), api_id, api_hash)\n", "    \n", "    try:\n", "        # Connect to Telegram\n", "        await client.connect()\n", "        \n", "        # Check if user is authorized\n", "        if not await client.is_user_authorized():\n", "            await client.send_code_request(phone)\n", "            code = input('Enter the SMS code: ')\n", "            \n", "            try:\n", "                await client.sign_in(phone, code)\n", "            except SessionPasswordNeededError:\n", "                # Two-step verification is enabled, need password\n", "                password = input('Enter your 2FA password: ')\n", "                await client.sign_in(password=password)\n", "        \n", "        print('Fetching Members...')\n", "        \n", "        # Enter target group or channel\n", "        target = '@airdropforall'\n", "        \n", "        # Get participants (this is also async)\n", "        all_participants = await client.get_participants(target, aggressive=True)\n", "        \n", "        print('Saving In file...')\n", "        \n", "        # Save to CSV\n", "        with open(\"data.csv\", \"w\", encoding='UTF-8') as f:\n", "            writer = csv.writer(f, delimiter=\",\", lineterminator=\"\\n\")\n", "            writer.writerow(['sr. no.', 'username', 'user id', 'name', 'group', 'group id'])\n", "            i = 0\n", "            \n", "            for user in all_participants:\n", "                i += 1\n", "                \n", "                # Handle username\n", "                if user.username:\n", "                    username = user.username\n", "                else:\n", "                    username = \"\"\n", "                \n", "                # Handle first name\n", "                if user.first_name:\n", "                    first_name = user.first_name\n", "                else:\n", "                    first_name = \"\"\n", "                \n", "                # Handle last name\n", "                if user.last_name:\n", "                    last_name = user.last_name\n", "                else:\n", "                    last_name = \"\"\n", "                \n", "                # Combine names\n", "                name = (first_name + ' ' + last_name).strip()\n", "                \n", "                # Write to CSV\n", "                writer.writerow([i, username, user.id, name, target, 'groupid'])\n", "        \n", "        print(f'Members scraped successfully. Total members: {len(all_participants)}')\n", "        \n", "    except Exception as e:\n", "        print(f\"An error occurred: {e}\")\n", "        traceback.print_exc()\n", "    \n", "    finally:\n", "        # Always disconnect\n", "        await client.disconnect()\n", "\n", "# Run the async function\n", "await scrape_telegram_members()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from openai import OpenAI\n", "from tavily import TavilyClient\n", "import re\n", "from dotenv import load_dotenv\n", "from logger_config import setup_logger\n", "\n", "load_dotenv()\n", "logger = setup_logger()\n", "\n", "class SocialMediaPipeline:\n", "    def __init__(self, tavily_api_key=None, openai_api_key=None):\n", "        \"\"\"Initialize the pipeline with API keys.\"\"\"\n", "        logger.info(\"Initializing SocialMediaPipeline\")\n", "        self.tavily_api_key = tavily_api_key or os.environ.get(\"TAVILY_API_KEY\")\n", "        self.openai_api_key = openai_api_key or os.environ.get(\"OPENAI_API_KEY_v2\")\n", "    \n", "        if not self.tavily_api_key:\n", "            logger.error(\"Tavily API key is missing\")\n", "            raise ValueError(\"Tavily API key is required\")\n", "        if not self.openai_api_key:\n", "            logger.error(\"OpenAI API key is missing\")\n", "            raise ValueError(\"OpenAI API key is required\")\n", "        \n", "        self.tavily_client = TavilyClient(api_key=self.tavily_api_key)\n", "        self.openai_client = OpenAI(api_key=self.openai_api_key)\n", "        logger.info(\"Pipeline initialized successfully\")\n", "    \n", "    def scrape_website_content(self, company_url):\n", "        \"\"\"Scrape raw website content for processing with OpenAI.\"\"\"\n", "        logger.info(f\"Scraping website content from: {company_url}\")\n", "        try:\n", "            headers = {\n", "                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n", "            }\n", "            response = requests.get(company_url, headers=headers, timeout=10)\n", "            response.raise_for_status()\n", "            \n", "            soup = BeautifulSoup(response.text, 'html.parser')\n", "            \n", "            # Extract title\n", "            title = \"\"\n", "            title_tag = soup.find('title')\n", "            if title_tag and title_tag.text:\n", "                title = title_tag.text.strip()\n", "            \n", "            # Extract meta description\n", "            meta_description = \"\"\n", "            meta_desc = soup.find('meta', attrs={'name': 'description'})\n", "            if not meta_desc:\n", "                meta_desc = soup.find('meta', attrs={'property': 'og:description'})\n", "            if meta_desc and meta_desc.get('content'):\n", "                meta_description = meta_desc.get('content').strip()\n", "            \n", "            # Extract text content from paragraphs and headers\n", "            text_content = []\n", "            for tag in soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'li']):\n", "                if tag.text.strip():\n", "                    text_content.append(tag.text.strip())\n", "            \n", "            # Combine content with priority to meta information\n", "            website_content = {\n", "                'url': company_url,\n", "                'title': title,\n", "                'meta_description': meta_description,\n", "                'text_content': ' '.join(text_content[:100])  # Limit text to avoid token limits\n", "            }\n", "            \n", "            logger.info(f\"Successfully scraped content from {company_url}\")\n", "            return website_content\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"Error scraping {company_url}: {str(e)}\")\n", "            return None\n", "    \n", "    def extract_company_info_with_llm(self, website_content, company_goals=None):\n", "        \"\"\"Use OpenAI to extract structured company information from website content or goals.\"\"\"\n", "        logger.info(\"Extracting company information using LLM\")\n", "        if not website_content and not company_goals:\n", "            logger.warning(\"No website content or company goals provided\")\n", "            return None\n", "        \n", "        prompt = f\"\"\"\n", "        Extract key business information from this company information.\n", "        \n", "        {'Website Content:' if website_content else 'Company Goals:'}\n", "        {website_content['text_content'] if website_content else company_goals}\n", "        \n", "        Please extract and format the following information in JSON format:\n", "        \n", "        1. name: The company name\n", "        2. description: A 1-2 sentence description of what the company does\n", "        3. keywords: 5-10 keywords relevant to the company's business\n", "        4. industry: The primary industry or business sector\n", "        5. services: List of main products/services offered\n", "        6. location: Geographic focus or headquarters location if mentioned\n", "        7. target_audience: The company's target customers or users\n", "        8. country: The primary country of operation\n", "        \n", "        Format the response as valid JSON.\n", "\n", "        DO NOT ADD ANYTHING ELSE TO THE JSON RESPONSE.\n", "\n", "        NOTE: MAKE SURE YOUR ENTRIED RESPONSE DO NOT EXCEED 350 CHARACTERS IN TOTAL\n", "        \"\"\"\n", "        \n", "        try:\n", "            response = self.openai_client.chat.completions.create(\n", "                model=\"gpt-4\",\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": \"You are an AI assistant specialized in extracting business information. Return only valid JSON.\"},\n", "                    {\"role\": \"user\", \"content\": prompt}\n", "                ]\n", "            )\n", "            \n", "            # Parse the JSON response\n", "            company_info = json.loads(response.choices[0].message.content)\n", "            logger.info(\"Successfully extracted company information\")\n", "            return company_info\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"Error extracting company info with LLM: {str(e)}\")\n", "            return None\n", "    \n", "    def search_telegram_groups(self, company_info):\n", "        \"\"\"Search for telegram groups using Tavily API.\"\"\"\n", "        logger.info(\"Searching for telegram groups\")\n", "        if not company_info:\n", "            logger.warning(\"No company info provided for telegram group search\")\n", "            return []\n", "        \n", "        # Construct search query using company info\n", "        query = f\"telegram groups {company_info.get('industry', '')} {company_info.get('services', [])} {company_info.get('country', '')}\"\n", "        logger.debug(f\"Search query: {query}\")\n", "        \n", "        try:\n", "            # Search with <PERSON><PERSON>\n", "            search_results = self.tavily_client.search(query, max_results=4)\n", "            \n", "            telegram_groups = []\n", "            non_telegram_urls = []\n", "            \n", "            # Process results\n", "            for result in search_results.get('results', []):\n", "                url = result.get('url', '')\n", "                if 't.me/' in url or 'telegram.me/' in url:\n", "                    telegram_groups.append({\n", "                        'url': url,\n", "                        'title': result.get('title', ''),\n", "                        'description': result.get('content', '')\n", "                    })\n", "                else:\n", "                    non_telegram_urls.append(url)\n", "            \n", "            logger.info(f\"Found {len(telegram_groups)} direct telegram groups\")\n", "            \n", "            # Scrape non-telegram websites for telegram links\n", "            for url in non_telegram_urls:\n", "                try:\n", "                    logger.debug(f\"Scraping {url} for telegram links\")\n", "                    headers = {\n", "                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n", "                    }\n", "                    response = requests.get(url, headers=headers, timeout=10)\n", "                    soup = BeautifulSoup(response.text, 'html.parser')\n", "                    \n", "                    # Find all links\n", "                    for link in soup.find_all('a', href=True):\n", "                        href = link['href']\n", "                        if 't.me/' in href or 'telegram.me/' in href:\n", "                            telegram_groups.append({\n", "                                'url': href,\n", "                                'title': link.text.strip() or 'Telegram Group',\n", "                                'description': 'Found on ' + url\n", "                            })\n", "                except Exception as e:\n", "                    logger.error(f\"Error scraping {url} for telegram links: {str(e)}\")\n", "                    continue\n", "            \n", "            logger.info(f\"Total telegram groups found: {len(telegram_groups)}\")\n", "            return telegram_groups\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"Error searching for telegram groups: {str(e)}\")\n", "            return []\n", "\n", "    def search_whatsapp_groups(self, company_info):\n", "        \"\"\"Search for WhatsApp groups using Tavily API.\"\"\"\n", "        logger.info(\"Searching for WhatsApp groups\")\n", "        if not company_info:\n", "            logger.warning(\"No company info provided for WhatsApp group search\")\n", "            return []\n", "        \n", "        # Construct search query using company info\n", "        query = f\"whatsapp groups {company_info.get('industry', '')} {company_info.get('services', [])} {company_info.get('country', '')}\"\n", "        logger.debug(f\"Search query: {query}\")\n", "        \n", "        try:\n", "            # Search with <PERSON><PERSON>\n", "            search_results = self.tavily_client.search(query, max_results=4)\n", "            \n", "            whatsapp_groups = []\n", "            non_whatsapp_urls = []\n", "            \n", "            # Process results\n", "            for result in search_results.get('results', []):\n", "                url = result.get('url', '')\n", "                content = result.get('content', '')\n", "                \n", "                # Look for WhatsApp group links in the content\n", "                whatsapp_links = re.findall(r'https?://(?:chat\\.whatsapp\\.com|wa\\.me)/[a-zA-Z0-9]+', content)\n", "                if whatsapp_links:\n", "                    for link in whatsapp_links:\n", "                        whatsapp_groups.append({\n", "                            'url': link,\n", "                            'title': result.get('title', ''),\n", "                            'description': result.get('content', '')\n", "                        })\n", "                else:\n", "                    non_whatsapp_urls.append(url)\n", "            \n", "            logger.info(f\"Found {len(whatsapp_groups)} direct WhatsApp groups\")\n", "            \n", "            # Scrape non-WhatsApp websites for WhatsApp links\n", "            for url in non_whatsapp_urls:\n", "                try:\n", "                    logger.debug(f\"Scraping {url} for WhatsApp links\")\n", "                    headers = {\n", "                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n", "                    }\n", "                    response = requests.get(url, headers=headers, timeout=10)\n", "                    soup = BeautifulSoup(response.text, 'html.parser')\n", "                    \n", "                    # Find all links\n", "                    for link in soup.find_all('a', href=True):\n", "                        href = link['href']\n", "                        if 'chat.whatsapp.com' in href or 'wa.me' in href:\n", "                            whatsapp_groups.append({\n", "                                'url': href,\n", "                                'title': link.text.strip() or 'WhatsApp Group',\n", "                                'description': 'Found on ' + url\n", "                            })\n", "                except Exception as e:\n", "                    logger.error(f\"Error scraping {url} for WhatsApp links: {str(e)}\")\n", "                    continue\n", "            \n", "            logger.info(f\"Total WhatsApp groups found: {len(whatsapp_groups)}\")\n", "            return whatsapp_groups\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"Error searching for WhatsApp groups: {str(e)}\")\n", "            return []\n", "\n", "    def search_twitter_links(self, company_info):\n", "        \"\"\"Search for Twitter usernames and construct profile URLs.\"\"\"\n", "        logger.info(\"Searching for Twitter usernames\")\n", "        if not company_info:\n", "            logger.warning(\"No company info provided for Twitter search\")\n", "            return []\n", "        \n", "        # Construct search query using company info\n", "        company_name = company_info.get('name', '').lower()\n", "        query = f\"twitter username {company_name} {company_info.get('industry', '')} {company_info.get('country', '')}\"\n", "        logger.debug(f\"Search query: {query}\")\n", "        \n", "        try:\n", "            # Search with <PERSON><PERSON>\n", "            search_results = self.tavily_client.search(query, max_results=4)\n", "            \n", "            twitter_profiles = []\n", "            seen_usernames = set()\n", "            \n", "            # Process results\n", "            for result in search_results.get('results', []):\n", "                content = result.get('content', '')\n", "                title = result.get('title', '')\n", "                \n", "                # Look for Twitter usernames in content\n", "                # Pattern for @username or twitter.com/username\n", "                username_patterns = [\n", "                    r'@([a-zA-Z0-9_]{1,15})',  # @username\n", "                    r'(?:twitter\\.com|x\\.com)/([a-zA-Z0-9_]{1,15})',  # twitter.com/username\n", "                    r'Twitter: @([a-zA-Z0-9_]{1,15})',  # Twitter: @username\n", "                    r'Twitter handle: @([a-zA-Z0-9_]{1,15})'  # Twitter handle: @username\n", "                ]\n", "                \n", "                for pattern in username_patterns:\n", "                    usernames = re.findall(pattern, content)\n", "                    for username in usernames:\n", "                        username = username.lower()  # Normalize username to lowercase\n", "                        if username not in seen_usernames:\n", "                            seen_usernames.add(username)\n", "                            # Construct Twitter profile URL\n", "                            profile_url = f\"https://twitter.com/{username}\"\n", "                            twitter_profiles.append({\n", "                                'url': profile_url,\n", "                                'username': username,\n", "                                'title': title,\n", "                                'description': f\"Twitter profile for {company_name}\",\n", "                                'type': 'profile'\n", "                            })\n", "            \n", "            # If no usernames found, try to extract from company name\n", "            if not twitter_profiles and company_name:\n", "                # Try common variations of company name\n", "                possible_usernames = [\n", "                    company_name.replace(' ', ''),  # Remove spaces\n", "                    company_name.replace(' ', '_'),  # Replace spaces with underscores\n", "                    ''.join(word[0] for word in company_name.split()),  # Acronym\n", "                    company_name.split()[0]  # First word\n", "                ]\n", "                \n", "                for username in possible_usernames:\n", "                    username = username.lower()\n", "                    if username not in seen_usernames and len(username) <= 15:\n", "                        seen_usernames.add(username)\n", "                        profile_url = f\"https://twitter.com/{username}\"\n", "                        twitter_profiles.append({\n", "                            'url': profile_url,\n", "                            'username': username,\n", "                            'title': f\"Possible Twitter profile for {company_name}\",\n", "                            'description': f\"Constructed Twitter profile URL based on company name\",\n", "                            'type': 'profile'\n", "                        })\n", "            \n", "            logger.info(f\"Found {len(twitter_profiles)} Twitter profiles\")\n", "            return twitter_profiles\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"Error searching for Twitter profiles: {str(e)}\")\n", "            return []\n", "    \n", "    def run_pipeline(self, company_url=None, company_goals=None):\n", "        \"\"\"Run the complete pipeline.\"\"\"\n", "        logger.info(\"Starting pipeline execution\")\n", "        if not company_url and not company_goals:\n", "            logger.error(\"Neither company_url nor company_goals provided\")\n", "            raise ValueError(\"Either company_url or company_goals must be provided\")\n", "        \n", "        # Step 1: Scrape website content if URL provided\n", "        website_content = None\n", "        if company_url:\n", "            logger.info(f\"Processing company URL: {company_url}\")\n", "            website_content = self.scrape_website_content(company_url)\n", "        \n", "        # Step 2: Extract company info\n", "        company_info = self.extract_company_info_with_llm(website_content, company_goals)\n", "        \n", "        # Step 3: Search for social media links\n", "        telegram_groups = self.search_telegram_groups(company_info)\n", "        whatsapp_groups = self.search_whatsapp_groups(company_info)\n", "        twitter_links = self.search_twitter_links(company_info)\n", "        \n", "        logger.info(\"Pipeline execution completed\")\n", "        return {\n", "            \"company_info\": company_info,\n", "            \"telegram_groups\": telegram_groups,\n", "            \"whatsapp_groups\": whatsapp_groups,\n", "            \"twitter_links\": twitter_links\n", "        }\n", "\n", "# Example usage\n", "if __name__ == \"__main__\":\n", "    logger.info(\"Starting main execution\")\n", "    # Set your API keys in environment variables or pass directly\n", "    pipeline = SocialMediaPipeline(\n", "        tavily_api_key=os.environ.get(\"TAVILY_API_KEY\"),\n", "        openai_api_key=os.environ.get(\"OPENAI_API_KEY_v2\")\n", "    )\n", "    \n", "    # Run with company URL\n", "    logger.info(\"Running pipeline with company URL\")\n", "    url_results = pipeline.run_pipeline(\n", "        company_url=\"https://www.afriexapp.com/\"\n", "    )\n", "    \n", "    # Run with company goals\n", "    logger.info(\"Running pipeline with company goals\")\n", "    goals_results = {\"telegram_groups\": [], \"whatsapp_groups\": [], \"twitter_links\": []}\n", "    \n", "    # Combine results and remove duplicates based on URL\n", "    logger.info(\"Combining results from both searches\")\n", "    combined_results = {\n", "        \"telegram_groups\": [],\n", "        \"whatsapp_groups\": [],\n", "        \"twitter_links\": []\n", "    }\n", "    \n", "    # Process each social media type\n", "    for media_type in combined_results.keys():\n", "        seen_urls = set()\n", "        for result in url_results[media_type] + goals_results[media_type]:\n", "            if result[\"url\"] not in seen_urls:\n", "                seen_urls.add(result[\"url\"])\n", "                combined_results[media_type].append(result)\n", "    \n", "    logger.info(f\"Found {len(combined_results['telegram_groups'])} unique telegram groups\")\n", "    logger.info(f\"Found {len(combined_results['whatsapp_groups'])} unique WhatsApp groups\")\n", "    logger.info(f\"Found {len(combined_results['twitter_links'])} unique Twitter links\")\n", "    \n", "    print(\"\\nCombined Results from both URL and Goals searches:\")\n", "    print(json.dumps(combined_results, indent=2))\n", "    logger.info(\"Main execution completed\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "asyncio.run() cannot be called from a running event loop", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[17], line 141\u001b[0m\n\u001b[1;32m    138\u001b[0m         \u001b[38;5;28;<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m scraper\u001b[38;5;241m.\u001b[39mclose()\n\u001b[1;32m    140\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m--> 141\u001b[0m     \u001b[43masyncio\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/anaconda3/envs/dgen/lib/python3.10/asyncio/runners.py:33\u001b[0m, in \u001b[0;36mrun\u001b[0;34m(main, debug)\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Execute the coroutine and return the result.\u001b[39;00m\n\u001b[1;32m     10\u001b[0m \n\u001b[1;32m     11\u001b[0m \u001b[38;5;124;03mThis function runs the passed coroutine, taking care of\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     30\u001b[0m \u001b[38;5;124;03m    asyncio.run(main())\u001b[39;00m\n\u001b[1;32m     31\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m     32\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m events\u001b[38;5;241m.\u001b[39m_get_running_loop() \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[0;32m---> 33\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRunt<PERSON><PERSON>rror\u001b[39;00m(\n\u001b[1;32m     34\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124masyncio.run() cannot be called from a running event loop\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     36\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m coroutines\u001b[38;5;241m.\u001b[39miscoroutine(main):\n\u001b[1;32m     37\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124ma coroutine was expected, got \u001b[39m\u001b[38;5;132;01m{!r}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(main))\n", "\u001b[0;31mRuntimeError\u001b[0m: asyncio.run() cannot be called from a running event loop"]}], "source": ["# Fixed code for Google Colab - arranged by Solved4You 2.0\n", "from telethon import TelegramClient\n", "from telethon.tl.functions.messages import GetDialogsRequest\n", "from telethon.tl.types import InputPeerEmpty, InputPeerChannel, InputPeerUser\n", "from telethon.errors.rpcerrorlist import PeerFloodError, UserPrivacyRestrictedError, SessionPasswordNeededError\n", "from telethon.tl.functions.channels import InviteToChannelRequest\n", "from typing import List, Dict, Optional, Any\n", "import logging\n", "import asyncio\n", "from dataclasses import dataclass\n", "\n", "# Configure logging\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "@dataclass\n", "class TelegramConfig:\n", "    \"\"\"Configuration for Telegram client\"\"\"\n", "    api_id: str\n", "    api_hash: str\n", "    phone: str\n", "    session_name: str = 'telegram_scrape'\n", "\n", "@dataclass\n", "class TelegramMember:\n", "    \"\"\"Data structure for Telegram member information\"\"\"\n", "    username: str\n", "    user_id: int\n", "    first_name: str\n", "    last_name: str\n", "    group: str\n", "    group_id: str\n", "\n", "    @property\n", "    def full_name(self) -> str:\n", "        \"\"\"Get the full name of the member\"\"\"\n", "        return f\"{self.first_name} {self.last_name}\".strip()\n", "\n", "    def to_dict(self) -> Dict[str, Any]:\n", "        \"\"\"Convert member data to dictionary\"\"\"\n", "        return {\n", "            'username': self.username,\n", "            'user_id': self.user_id,\n", "            'first_name': self.first_name,\n", "            'last_name': self.last_name,\n", "            'full_name': self.full_name,\n", "            'group': self.group,\n", "            'group_id': self.group_id\n", "        }\n", "\n", "class TelegramScraper:\n", "    \"\"\"Telegram scraper for collecting member information from groups/channels\"\"\"\n", "    \n", "    def __init__(self, config: TelegramConfig):\n", "        self.config = config\n", "        self.client = TelegramClient(config.session_name, config.api_id, config.api_hash)\n", "\n", "    async def connect(self) -> None:\n", "        \"\"\"Connect to Telegram and handle authentication\"\"\"\n", "        try:\n", "            await self.client.connect()\n", "            \n", "            if not await self.client.is_user_authorized():\n", "                await self.client.send_code_request(self.config.phone)\n", "                code = input('Enter the SMS code: ')\n", "                \n", "                try:\n", "                    await self.client.sign_in(self.config.phone, code)\n", "                except SessionPasswordNeededError:\n", "                    password = input('Enter your 2FA password: ')\n", "                    await self.client.sign_in(password=password)\n", "                    \n", "            logger.info(\"Successfully connected to Telegram\")\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"Failed to connect to Telegram: {str(e)}\")\n", "            raise\n", "\n", "    async def scrape_members(self, target: str) -> List[Dict[str, Any]]:\n", "        \"\"\"\n", "        Scrape members from a target group/channel\n", "        \n", "        Args:\n", "            target: The target group/channel username or ID\n", "            \n", "        Returns:\n", "            List of dictionaries containing member information\n", "        \"\"\"\n", "        try:\n", "            logger.info(f\"Fetching members from {target}\")\n", "            \n", "            # Get participants\n", "            all_participants = await self.client.get_participants(target, aggressive=True)\n", "            \n", "            # Convert to structured data\n", "            members = []\n", "            for user in all_participants:\n", "                member = TelegramMember(\n", "                    username=user.username or \"\",\n", "                    user_id=user.id,\n", "                    first_name=user.first_name or \"\",\n", "                    last_name=user.last_name or \"\",\n", "                    group=target,\n", "                    group_id='groupid'  # You might want to get the actual group ID\n", "                )\n", "                members.append(member.to_dict())\n", "            \n", "            logger.info(f\"Successfully scraped {len(members)} members from {target}\")\n", "            return members\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"Error scraping members: {str(e)}\")\n", "            raise\n", "\n", "    async def close(self) -> None:\n", "        \"\"\"Close the Telegram client connection\"\"\"\n", "        await self.client.disconnect()\n", "        logger.info(\"Telegram client disconnected\")\n", "\n", "async def main():\n", "    \"\"\"Main function to run the scraper\"\"\"\n", "    config = TelegramConfig(\n", "        api_id='********',\n", "        api_hash='f9ab963bd50e9669e6ea7d7da4da1e0f',\n", "        phone='+*************'\n", "    )\n", "    \n", "    scraper = TelegramScraper(config)\n", "    \n", "    try:\n", "        await scraper.connect()\n", "        members = await scraper.scrape_members('@CryptoFlake')\n", "        return members\n", "    finally:\n", "        await scraper.close()\n", "\n", "if __name__ == \"__main__\":\n", "    asyncio.run(main())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import whatsapp_api  # Hypothetical library\n", "\n", "# Authentication\n", "whatsapp = whatsapp_api.WhatsAppClient(phone_number=\"YOUR_PHONE_NUMBER\", password=\"YOUR_PASSWORD\")\n", "whatsapp.login()\n", "\n", "# Retrieve groups\n", "groups = whatsapp.get_my_groups()\n", "\n", "# Process groups\n", "for group in groups:\n", "    print(f\"Group Name: {group['name']}, ID: {group['id']}\")\n", "\n", "whatsapp.logout()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Authentication token set\n", "💡 Token format: IvvTzJXb9h...i8WRDceQnU\n", "\n", "📡 Checking connection status...\n", "⚠️ Status: AUTH (Code: 4)\n", "\n", "⚠️ Status Code 4 - Not fully connected, but let's try anyway...\n", "🔬 Testing if group scraping works despite connection status...\n", "\n", "🎯 Attempting group scraping...\n", "🚀 Starting group member scraping...\n", "✅ Found 1 groups\n", "\n", "📱 Processing group: #Me time\n", "✅ Group '#Me time' has 2 participants\n", "📋 Participants in '#Me time':\n", "   • 2349157611271 (member)\n", "   • ************* (creator)\n", "\n", "📊 Summary:\n", "   • Total groups: 1\n", "   • Unique participants: 2\n", "   • Total admin roles: 1\n", "✅ Data exported to whatsapp_group_members.json\n", "✅ Data exported to group_summary.json\n", "\n", "✅ Scraping completed successfully!\n", "📁 Check these files for your data:\n", "   • whatsapp_group_members.json\n", "   • group_summary.json\n"]}], "source": ["import requests\n", "import json\n", "import time\n", "from typing import List, Dict, Optional\n", "\n", "class WhatsAppGroupScraper:\n", "    def __init__(self, base_url: str = \"https://gate.whapi.cloud\"):\n", "        self.base_url = base_url\n", "        self.token = None\n", "        self.headers = {\n", "            \"accept\": \"application/json\",\n", "            \"content-type\": \"application/json\"\n", "        }\n", "    \n", "    def get_qr_code(self) -> Dict:\n", "        \"\"\"\n", "        Step 1: Get QR code for device linking\n", "        \n", "        Note: You need to have a Whapi.Cloud account and channel set up first.\n", "        This method gets the QR code that you scan with WhatsApp mobile app.\n", "        \n", "        Returns:\n", "            Dict: QR code data\n", "        \"\"\"\n", "        if not self.token:\n", "            print(\"❌ No authentication token set. Please set your API token first.\")\n", "            return {}\n", "            \n", "        url = f\"{self.base_url}/users/login?token={self.token}\"\n", "        \n", "        try:\n", "            response = requests.get(url, headers=self.headers)\n", "            response.raise_for_status()\n", "            \n", "            qr_data = response.json()\n", "            print(f\"✅ QR code retrieved successfully\")\n", "            print(f\"Scan this QR code with WhatsApp: Settings > Linked Devices > Link Device\")\n", "            \n", "            return qr_data\n", "            \n", "        except requests.exceptions.RequestException as e:\n", "            print(f\"❌ Failed to get QR code: {e}\")\n", "            print(\"💡 Make sure you have a valid API token from your Whapi.Cloud dashboard\")\n", "            return {}\n", "    \n", "    def set_auth_token(self, token: str):\n", "        \"\"\"\n", "        Set authentication token for API requests\n", "        \n", "        Args:\n", "            token (str): API token from your Whapi.Cloud dashboard\n", "        \"\"\"\n", "        self.token = token\n", "        print(f\"✅ Authentication token set\")\n", "        print(f\"💡 Token format: {token[:10]}...{token[-10:] if len(token) > 20 else token}\")\n", "    \n", "    def check_connection_status(self) -> Dict:\n", "        \"\"\"\n", "        Check if WhatsApp is connected and ready\n", "        \n", "        Returns:\n", "            Dict: Connection status information\n", "        \"\"\"\n", "        if not self.token:\n", "            print(\"❌ No authentication token set\")\n", "            return {}\n", "            \n", "        url = f\"{self.base_url}/health?token={self.token}\"\n", "        \n", "        try:\n", "            response = requests.get(url, headers=self.headers)\n", "            response.raise_for_status()\n", "            \n", "            status_data = response.json()\n", "            status_code = status_data.get('status', {}).get('code', 0)\n", "            status_text = status_data.get('status', {}).get('text', 'Unknown')\n", "            \n", "            if status_code == 3:\n", "                print(\"📱 Status: Waiting for QR code scan\")\n", "            elif status_code == 5:\n", "                print(\"❌ Status: <PERSON><PERSON><PERSON> - <PERSON><PERSON> not connected\")\n", "            elif status_code == 7:\n", "                print(\"✅ Status: Connected and ready!\")\n", "            else:\n", "                print(f\"⚠️ Status: {status_text} (Code: {status_code})\")\n", "            \n", "            return status_data\n", "            \n", "        except requests.exceptions.RequestException as e:\n", "            print(f\"❌ Failed to check status: {e}\")\n", "            return {}\n", "    \n", "    def get_user_groups(self) -> List[Dict]:\n", "        \"\"\"\n", "        Step 2: Get all groups the authenticated user is part of\n", "        \n", "        Returns:\n", "            List[Dict]: List of groups with basic information\n", "        \"\"\"\n", "        if not self.token:\n", "            print(\"❌ No authentication token set\")\n", "            return []\n", "        \n", "        url = f\"{self.base_url}/groups?token={self.token}\"\n", "        \n", "        try:\n", "            response = requests.get(url, headers=self.headers)\n", "            response.raise_for_status()\n", "            \n", "            groups_data = response.json()\n", "            groups = groups_data.get('groups', []) if isinstance(groups_data, dict) else groups_data\n", "            \n", "            print(f\"✅ Found {len(groups)} groups\")\n", "            return groups\n", "            \n", "        except requests.exceptions.RequestException as e:\n", "            print(f\"❌ Failed to get groups: {e}\")\n", "            return []\n", "    \n", "    def get_group_details(self, group_id: str) -> Dict:\n", "        \"\"\"\n", "        Step 3: Get detailed information about a specific group including all participants\n", "        \n", "        Args:\n", "            group_id (str): Group ID (e.g., \"<EMAIL>\")\n", "            \n", "        Returns:\n", "            Dict: Detailed group information with participants\n", "        \"\"\"\n", "        if not self.token:\n", "            print(\"❌ No authentication token set\")\n", "            return {}\n", "        \n", "        url = f\"{self.base_url}/groups/{group_id}?token={self.token}\"\n", "        \n", "        try:\n", "            response = requests.get(url, headers=self.headers)\n", "            response.raise_for_status()\n", "            \n", "            group_details = response.json()\n", "            participants = group_details.get('participants', [])\n", "            \n", "            print(f\"✅ Group '{group_details.get('name', 'Unknown')}' has {len(participants)} participants\")\n", "            return group_details\n", "            \n", "        except requests.exceptions.RequestException as e:\n", "            print(f\"❌ Failed to get group details for {group_id}: {e}\")\n", "            return {}\n", "    \n", "    def scrape_all_group_members(self) -> Dict[str, List[Dict]]:\n", "        \"\"\"\n", "        Complete workflow: Scrape all members from all groups the user is part of\n", "        \n", "        Returns:\n", "            Dict: Dictionary with group names as keys and participant lists as values\n", "        \"\"\"\n", "        print(\"🚀 Starting group member scraping...\")\n", "        \n", "        # Get all user groups\n", "        groups = self.get_user_groups()\n", "        if not groups:\n", "            print(\"❌ No groups found or failed to retrieve groups\")\n", "            return {}\n", "        \n", "        all_group_members = {}\n", "        \n", "        for group in groups:\n", "            group_id = group.get('id')\n", "            group_name = group.get('name', 'Unknown Group')\n", "            \n", "            if not group_id:\n", "                print(f\"⚠️ Skipping group without ID: {group_name}\")\n", "                continue\n", "            \n", "            print(f\"\\n📱 Processing group: {group_name}\")\n", "            \n", "            # Get detailed group information\n", "            group_details = self.get_group_details(group_id)\n", "            \n", "            if group_details:\n", "                participants = group_details.get('participants', [])\n", "                all_group_members[group_name] = participants\n", "                \n", "                print(f\"📋 Participants in '{group_name}':\")\n", "                for participant in participants:\n", "                    phone = participant.get('id', 'Unknown')\n", "                    rank = participant.get('rank', 'member')\n", "                    print(f\"   • {phone} ({rank})\")\n", "            \n", "            # Add delay to avoid rate limiting\n", "            time.sleep(1)\n", "        \n", "        return all_group_members\n", "    \n", "    def export_to_json(self, data: Dict, filename: str = \"group_members.json\"):\n", "        \"\"\"\n", "        Export scraped data to JSON file\n", "        \n", "        Args:\n", "            data (Dict): Group members data\n", "            filename (str): Output filename\n", "        \"\"\"\n", "        try:\n", "            with open(filename, 'w', encoding='utf-8') as f:\n", "                json.dump(data, f, indent=2, ensure_ascii=False)\n", "            print(f\"✅ Data exported to {filename}\")\n", "        except Exception as e:\n", "            print(f\"❌ Failed to export data: {e}\")\n", "    \n", "    def get_participant_summary(self, all_group_members: Dict) -> Dict:\n", "        \"\"\"\n", "        Generate summary statistics of all participants\n", "        \n", "        Args:\n", "            all_group_members (Dict): Group members data\n", "            \n", "        Returns:\n", "            Dict: Summary statistics\n", "        \"\"\"\n", "        total_groups = len(all_group_members)\n", "        all_participants = set()\n", "        admin_count = 0\n", "        \n", "        for group_name, participants in all_group_members.items():\n", "            for participant in participants:\n", "                phone = participant.get('id')\n", "                rank = participant.get('rank', 'member')\n", "                \n", "                if phone:\n", "                    all_participants.add(phone)\n", "                \n", "                if rank in ['admin', 'creator']:\n", "                    admin_count += 1\n", "        \n", "        summary = {\n", "            \"total_groups\": total_groups,\n", "            \"unique_participants\": len(all_participants),\n", "            \"total_admin_roles\": admin_count,\n", "            \"groups_breakdown\": {\n", "                group: len(participants) \n", "                for group, participants in all_group_members.items()\n", "            }\n", "        }\n", "        \n", "        return summary\n", "\n", "\n", "# Example usage\n", "def main():\n", "    \"\"\"\n", "    Example implementation of the WhatsApp group scraper\n", "    \"\"\"\n", "    # Initialize scraper\n", "    scraper = WhatsAppGroupScraper()\n", "    \n", "    # Step 1: Set your API token (REPLACE WITH YOUR ACTUAL TOKEN)\n", "    auth_token = \"IvvTzJXb9hPkunTFBOhptCi8WRDceQnU\"  # Your token here\n", "    scraper.set_auth_token(auth_token)\n", "    \n", "    # Step 2: Check connection status\n", "    print(\"\\n📡 Checking connection status...\")\n", "    status = scraper.check_connection_status()\n", "    \n", "    # Step 3: Try to scrape regardless of connection status (for testing)\n", "    status_code = status.get('status', {}).get('code', 0)\n", "    if status_code != 7:  # 7 means fully connected\n", "        print(f\"\\n⚠️ Status Code {status_code} - Not fully connected, but let's try anyway...\")\n", "        print(\"🔬 Testing if group scraping works despite connection status...\")\n", "    \n", "    # Step 4: Try to scrape all group members \n", "    print(\"\\n🎯 Attempting group scraping...\")\n", "    all_members = scraper.scrape_all_group_members()\n", "    \n", "    # Step 5: Generate summary\n", "    if all_members:\n", "        summary = scraper.get_participant_summary(all_members)\n", "        print(f\"\\n📊 Summary:\")\n", "        print(f\"   • Total groups: {summary['total_groups']}\")\n", "        print(f\"   • Unique participants: {summary['unique_participants']}\")\n", "        print(f\"   • Total admin roles: {summary['total_admin_roles']}\")\n", "        \n", "        # Step 6: Export data\n", "        scraper.export_to_json(all_members, \"whatsapp_group_members.json\")\n", "        scraper.export_to_json(summary, \"group_summary.json\")\n", "        \n", "        print(\"\\n✅ Scraping completed successfully!\")\n", "        print(\"📁 Check these files for your data:\")\n", "        print(\"   • whatsapp_group_members.json\")\n", "        print(\"   • group_summary.json\")\n", "    else:\n", "        print(\"\\n⚠️ No group data found. Make sure you're part of some WhatsApp groups.\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'status': 'success', 'data': [{'postUrl': 'https://www.instagram.com/p/DJtyFcRM-Xn/', 'commentUrl': 'https://www.instagram.com/p/DJtyFcRM-Xn/c/18385389061190749', 'id': '18385389061190749', 'text': '👏👏👏', 'ownerUsername': 'feernandolluiz', 'ownerProfilePicUrl': 'https://scontent-atl3-3.cdninstagram.com/v/t51.2885-19/404565418_878743777079563_8405481283121920961_n.jpg?stp=dst-jpg_e0_s150x150_tt6&_nc_ht=scontent-atl3-3.cdninstagram.com&_nc_cat=107&_nc_oc=Q6cZ2QGOWGmokmMZQ-zsBahonfasQiAe1_V5eoBFIlgAMvtB6PQ1zEGe8tVn7YZsNrh4kwU&_nc_ohc=Eg_yBlHmWnMQ7kNvwE_z1Bk&_nc_gid=b3F0tqABw3xjHEZnCZUWOw&edm=AD93TDoBAAAA&ccb=7-5&oh=00_AfKVB-aIlOQnbt-iPffmdRiBU7-B6dTogp3YD4fSEIOdpw&oe=683F53B6&_nc_sid=87e5dd', 'timestamp': '2025-05-16T14:15:37.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'fbid_v2': '17841415606458067', 'full_name': 'Fernando Luiz', 'id': '15664536215', 'is_mentionable': True, 'is_private': False, 'is_verified': False, 'latest_reel_media': 1748563408, 'profile_pic_id': '3243914639082250536', 'profile_pic_url': 'https://scontent-atl3-3.cdninstagram.com/v/t51.2885-19/404565418_878743777079563_8405481283121920961_n.jpg?stp=dst-jpg_e0_s150x150_tt6&_nc_ht=scontent-atl3-3.cdninstagram.com&_nc_cat=107&_nc_oc=Q6cZ2QGOWGmokmMZQ-zsBahonfasQiAe1_V5eoBFIlgAMvtB6PQ1zEGe8tVn7YZsNrh4kwU&_nc_ohc=Eg_yBlHmWnMQ7kNvwE_z1Bk&_nc_gid=b3F0tqABw3xjHEZnCZUWOw&edm=AD93TDoBAAAA&ccb=7-5&oh=00_AfKVB-aIlOQnbt-iPffmdRiBU7-B6dTogp3YD4fSEIOdpw&oe=683F53B6&_nc_sid=87e5dd', 'username': 'feernandolluiz'}}, {'postUrl': 'https://www.instagram.com/p/DJtyFcRM-Xn/', 'commentUrl': 'https://www.instagram.com/p/DJtyFcRM-Xn/c/18106644103454610', 'id': '18106644103454610', 'text': '👏👏👏👏😮', 'ownerUsername': 'xradio_lenne', 'ownerProfilePicUrl': 'https://scontent-atl3-1.cdninstagram.com/v/t51.2885-19/427736159_378044861641942_9049205185415720791_n.jpg?stp=dst-jpg_e0_s150x150_tt6&_nc_ht=scontent-atl3-1.cdninstagram.com&_nc_cat=110&_nc_oc=Q6cZ2QGOWGmokmMZQ-zsBahonfasQiAe1_V5eoBFIlgAMvtB6PQ1zEGe8tVn7YZsNrh4kwU&_nc_ohc=w3RFTsa7U7YQ7kNvwEjyUbZ&_nc_gid=b3F0tqABw3xjHEZnCZUWOw&edm=AD93TDoBAAAA&ccb=7-5&oh=00_AfKhQZehnB--YlYL7jMMw9gip77BPFn1g9PYr0iVWqYIrw&oe=683F3F2C&_nc_sid=87e5dd', 'timestamp': '2025-05-16T23:56:53.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'fbid_v2': '17841462415000433', 'full_name': 'Lene Rocha', 'id': '62403464500', 'is_mentionable': True, 'is_private': True, 'is_verified': False, 'profile_pic_id': '3302138097079270243', 'profile_pic_url': 'https://scontent-atl3-1.cdninstagram.com/v/t51.2885-19/427736159_378044861641942_9049205185415720791_n.jpg?stp=dst-jpg_e0_s150x150_tt6&_nc_ht=scontent-atl3-1.cdninstagram.com&_nc_cat=110&_nc_oc=Q6cZ2QGOWGmokmMZQ-zsBahonfasQiAe1_V5eoBFIlgAMvtB6PQ1zEGe8tVn7YZsNrh4kwU&_nc_ohc=w3RFTsa7U7YQ7kNvwEjyUbZ&_nc_gid=b3F0tqABw3xjHEZnCZUWOw&edm=AD93TDoBAAAA&ccb=7-5&oh=00_AfKhQZehnB--YlYL7jMMw9gip77BPFn1g9PYr0iVWqYIrw&oe=683F3F2C&_nc_sid=87e5dd', 'username': 'xradio_lenne'}}, {'postUrl': 'https://www.instagram.com/p/DJtyFcRM-Xn/', 'commentUrl': 'https://www.instagram.com/p/DJtyFcRM-Xn/c/17883979695294634', 'id': '17883979695294634', 'text': 'Mammography plays a crucial role in early detection and saving lives. 🎀 \\nThank you for continuing to innovate in ways that support women’s health worldwide! \\U0001fa77🎗️', 'ownerUsername': 'neuro.terra', 'ownerProfilePicUrl': 'https://scontent-atl3-1.cdninstagram.com/v/t51.2885-19/461928732_1999905923780859_1170489295458057360_n.jpg?stp=dst-jpg_e0_s150x150_tt6&_nc_ht=scontent-atl3-1.cdninstagram.com&_nc_cat=103&_nc_oc=Q6cZ2QGOWGmokmMZQ-zsBahonfasQiAe1_V5eoBFIlgAMvtB6PQ1zEGe8tVn7YZsNrh4kwU&_nc_ohc=LJO5qEKNJiQQ7kNvwERT5mz&_nc_gid=b3F0tqABw3xjHEZnCZUWOw&edm=AD93TDoBAAAA&ccb=7-5&oh=00_AfIhKJTDF2FhbQMBkUgo801hGBidByc6AHWU_8LdRX99yA&oe=683F41CC&_nc_sid=87e5dd', 'timestamp': '2025-05-26T18:28:40.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'fbid_v2': '17841462845941422', 'full_name': 'NEUROTERRA | Neuroscience • Medical Engineering • STEM', 'id': '62811382864', 'is_mentionable': True, 'is_private': False, 'is_verified': False, 'latest_reel_media': 0, 'profile_pic_id': '3471453724393969530', 'profile_pic_url': 'https://scontent-atl3-1.cdninstagram.com/v/t51.2885-19/461928732_1999905923780859_1170489295458057360_n.jpg?stp=dst-jpg_e0_s150x150_tt6&_nc_ht=scontent-atl3-1.cdninstagram.com&_nc_cat=103&_nc_oc=Q6cZ2QGOWGmokmMZQ-zsBahonfasQiAe1_V5eoBFIlgAMvtB6PQ1zEGe8tVn7YZsNrh4kwU&_nc_ohc=LJO5qEKNJiQQ7kNvwERT5mz&_nc_gid=b3F0tqABw3xjHEZnCZUWOw&edm=AD93TDoBAAAA&ccb=7-5&oh=00_AfIhKJTDF2FhbQMBkUgo801hGBidByc6AHWU_8LdRX99yA&oe=683F41CC&_nc_sid=87e5dd', 'username': 'neuro.terra'}}]}\n"]}], "source": ["from apify_client import ApifyClient\n", "import json\n", "\n", "def scrape_instagram_post(url: str, api_token: str, limit: int = 10) -> dict:\n", "    \"\"\"\n", "    Scrape Instagram post data using Apify actor\n", "    \n", "    Args:\n", "        url: Instagram post URL to scrape\n", "        api_token: Apify API token\n", "        limit: Maximum number of results to return\n", "        \n", "    Returns:\n", "        dict: Scraped data\n", "    \"\"\"\n", "    # Initialize client\n", "    client = ApifyClient(api_token)\n", "    \n", "    # Configure input\n", "    run_input = {\n", "        \"directUrls\": [url],\n", "        \"resultsLimit\": limit\n", "    }\n", "    \n", "    try:\n", "        # Run actor and get results\n", "        run = client.actor(\"SbK00X0JYCPblD2wp\").call(run_input=run_input)\n", "        \n", "        # Collect results\n", "        results = []\n", "        for item in client.dataset(run[\"defaultDatasetId\"]).iterate_items():\n", "            results.append(item)\n", "            \n", "        return {\n", "            \"status\": \"success\",\n", "            \"data\": results\n", "        }\n", "        \n", "    except Exception as e:\n", "        return {\n", "            \"status\": \"error\",\n", "            \"message\": str(e)\n", "        }\n", "\n", "# Example usage\n", "if __name__ == \"__main__\":\n", "    API_TOKEN = \"**********************************************\"\n", "    POST_URL = \"https://www.instagram.com/p/DJtyFcRM-Xn/\"\n", "    \n", "    result = scrape_instagram_post(POST_URL, API_TOKEN)\n", "    print(result)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'postUrl': 'https://www.instagram.com/p/DJtyFcRM-Xn/', 'commentUrl': 'https://www.instagram.com/p/DJtyFcRM-Xn/c/18385389061190749', 'id': '18385389061190749', 'text': '👏👏👏', 'ownerUsername': 'feernandolluiz', 'ownerProfilePicUrl': 'https://scontent-hou1-1.cdninstagram.com/v/t51.2885-19/404565418_878743777079563_8405481283121920961_n.jpg?stp=dst-jpg_e0_s150x150_tt6&_nc_cat=102&ccb=1-7&_nc_sid=f7ccc5&_nc_ohc=lAxZHwLObfgQ7kNvwHWLpkd&_nc_oc=AdlN4bv0xAbkmEV_twZfsFHKY9HNLsnXLSrSC1e8dceMPkruADOF3wC9EV1kV8_8ksk&_nc_ad=z-m&_nc_cid=0&_nc_zt=24&_nc_ht=scontent-hou1-1.cdninstagram.com&oh=00_AfIgqREduJuJl3hAWyy9YnpM9ZaU_jllcxjzRLkLvDQQmw&oe=683E72B6', 'timestamp': '2025-05-16T14:15:37.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'fbid_v2': '17841415606458067', 'full_name': 'Fernando Luiz', 'id': '15664536215', 'is_mentionable': True, 'is_private': False, 'is_verified': False, 'latest_reel_media': 1748461915, 'profile_pic_id': '3243914639082250536', 'profile_pic_url': 'https://scontent-hou1-1.cdninstagram.com/v/t51.2885-19/404565418_878743777079563_8405481283121920961_n.jpg?stp=dst-jpg_e0_s150x150_tt6&_nc_cat=102&ccb=1-7&_nc_sid=f7ccc5&_nc_ohc=lAxZHwLObfgQ7kNvwHWLpkd&_nc_oc=AdlN4bv0xAbkmEV_twZfsFHKY9HNLsnXLSrSC1e8dceMPkruADOF3wC9EV1kV8_8ksk&_nc_ad=z-m&_nc_cid=0&_nc_zt=24&_nc_ht=scontent-hou1-1.cdninstagram.com&oh=00_AfIgqREduJuJl3hAWyy9YnpM9ZaU_jllcxjzRLkLvDQQmw&oe=683E72B6', 'username': 'feernandolluiz'}}\n", "{'postUrl': 'https://www.instagram.com/p/DJtyFcRM-Xn/', 'commentUrl': 'https://www.instagram.com/p/DJtyFcRM-Xn/c/18106644103454610', 'id': '18106644103454610', 'text': '👏👏👏👏😮', 'ownerUsername': 'xradio_lenne', 'ownerProfilePicUrl': 'https://scontent-hou1-1.cdninstagram.com/v/t51.2885-19/427736159_378044861641942_9049205185415720791_n.jpg?stp=dst-jpg_e0_s150x150_tt6&_nc_cat=103&ccb=1-7&_nc_sid=f7ccc5&_nc_ohc=udPyIKdGLn4Q7kNvwH3pg9Y&_nc_oc=Adn1kIM0KT2UIz9KAEcfGpObLoXZr_b23L_AYhJ2o7BHcfbR3Bhpp7O5DNsIPDXODkw&_nc_ad=z-m&_nc_cid=0&_nc_zt=24&_nc_ht=scontent-hou1-1.cdninstagram.com&oh=00_AfIvrx98SLEz0PnGY5hXYw413IgaO7J80VakrC0Xl1jbrg&oe=683E5E2C', 'timestamp': '2025-05-16T23:56:53.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'fbid_v2': '17841462415000433', 'full_name': 'Lene Rocha', 'id': '62403464500', 'is_mentionable': True, 'is_private': True, 'is_verified': False, 'profile_pic_id': '3302138097079270243', 'profile_pic_url': 'https://scontent-hou1-1.cdninstagram.com/v/t51.2885-19/427736159_378044861641942_9049205185415720791_n.jpg?stp=dst-jpg_e0_s150x150_tt6&_nc_cat=103&ccb=1-7&_nc_sid=f7ccc5&_nc_ohc=udPyIKdGLn4Q7kNvwH3pg9Y&_nc_oc=Adn1kIM0KT2UIz9KAEcfGpObLoXZr_b23L_AYhJ2o7BHcfbR3Bhpp7O5DNsIPDXODkw&_nc_ad=z-m&_nc_cid=0&_nc_zt=24&_nc_ht=scontent-hou1-1.cdninstagram.com&oh=00_AfIvrx98SLEz0PnGY5hXYw413IgaO7J80VakrC0Xl1jbrg&oe=683E5E2C', 'username': 'xradio_lenne'}}\n", "{'postUrl': 'https://www.instagram.com/p/DJtyFcRM-Xn/', 'commentUrl': 'https://www.instagram.com/p/DJtyFcRM-Xn/c/17883979695294634', 'id': '17883979695294634', 'text': 'Mammography plays a crucial role in early detection and saving lives. 🎀 \\nThank you for continuing to innovate in ways that support women’s health worldwide! \\U0001fa77🎗️', 'ownerUsername': 'neuro.terra', 'ownerProfilePicUrl': 'https://scontent-hou1-1.cdninstagram.com/v/t51.2885-19/461928732_1999905923780859_1170489295458057360_n.jpg?stp=dst-jpg_e0_s150x150_tt6&_nc_cat=102&ccb=1-7&_nc_sid=f7ccc5&_nc_ohc=4ZNEx9015eEQ7kNvwF-qPyr&_nc_oc=AdnJsxnzSsDcAUdDPO4hm_EYRoPwNWBr6AqhF5OHZwCMXGFp7VnoW9biCUw8VGVV_gE&_nc_ad=z-m&_nc_cid=0&_nc_zt=24&_nc_ht=scontent-hou1-1.cdninstagram.com&oh=00_AfLr0jtBxPVZf_x1fS2xLKusmQNHA3HNQvSI3q9KPkOYdg&oe=683E60CC', 'timestamp': '2025-05-26T18:28:40.000Z', 'repliesCount': 0, 'replies': [], 'likesCount': 0, 'owner': {'fbid_v2': '17841462845941422', 'full_name': 'NEUROTERRA | Neuroscience • Medical Engineering • STEM', 'id': '62811382864', 'is_mentionable': True, 'is_private': False, 'is_verified': False, 'latest_reel_media': 0, 'profile_pic_id': '3471453724393969530', 'profile_pic_url': 'https://scontent-hou1-1.cdninstagram.com/v/t51.2885-19/461928732_1999905923780859_1170489295458057360_n.jpg?stp=dst-jpg_e0_s150x150_tt6&_nc_cat=102&ccb=1-7&_nc_sid=f7ccc5&_nc_ohc=4ZNEx9015eEQ7kNvwF-qPyr&_nc_oc=AdnJsxnzSsDcAUdDPO4hm_EYRoPwNWBr6AqhF5OHZwCMXGFp7VnoW9biCUw8VGVV_gE&_nc_ad=z-m&_nc_cid=0&_nc_zt=24&_nc_ht=scontent-hou1-1.cdninstagram.com&oh=00_AfLr0jtBxPVZf_x1fS2xLKusmQNHA3HNQvSI3q9KPkOYdg&oe=683E60CC', 'username': 'neuro.terra'}}\n"]}], "source": ["from apify_client import ApifyClient\n", "\n", "# Initialize the ApifyClient with your API token\n", "client = ApifyClient(\"**********************************************\")\n", "\n", "# Prepare the Actor input\n", "run_input = {\n", "    \"directUrls\": [\n", "        \"https://www.instagram.com/p/DJtyFcRM-Xn/\"\n", "    ],\n", "    \"resultsLimit\": 15,\n", "}\n", "\n", "# Run the Actor and wait for it to finish\n", "run = client.actor(\"SbK00X0JYCPblD2wp\").call(run_input=run_input)\n", "\n", "# Fetch and print Actor results from the run's dataset (if there are any)\n", "for item in client.dataset(run[\"defaultDatasetId\"]).iterate_items():\n", "    print(item)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "dgen", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}