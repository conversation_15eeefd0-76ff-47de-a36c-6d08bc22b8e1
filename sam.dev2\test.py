import os
from tavily import TavilyClient
import re
from dotenv import load_dotenv
from config.settings import Config
import openai

# Optionally load environment variables from a .env file
load_dotenv()

class SimilarCompanyFinder:
    def __init__(self, tavily_api_key=None, openai_api_key=None):
        # Load from config if not provided
        self.tavily_api_key = tavily_api_key or Config.TAVILY_API_KEY
        if not self.tavily_api_key:
            raise ValueError('Tavily API key is required')
        self.tavily_client = TavilyClient(api_key=self.tavily_api_key)
        self.openai_api_key = openai_api_key or Config.OPENAI_API_KEY
        if not self.openai_api_key:
            raise ValueError('OpenAI API key is required')
        self.openai_client = openai.OpenAI(api_key=self.openai_api_key)

    def call_openai(self, prompt, model="gpt-3.5-turbo", max_tokens=512):
        response = self.openai_client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=max_tokens,
            temperature=0.2
        )
        return response.choices[0].message.content

    def find_competitors(
        self,
        company_name=None,
        company_url=None,
        products_services=None
    ):
        # Step 1: Use Tavily to get web context
        query = f"Find up to 3 direct competitors for the company {company_name}"
        if company_url:
            query += f" ({company_url})"
        if products_services:
            query += f". The company does: {products_services}"
        query += ". Return only the company names and their official websites."
        search_results = self.tavily_client.search(query, max_results=10)
        print(f"Competitor search results: {search_results}")
        # Prepare context for OpenAI
        context = "\n\n".join([
            f"Title: {r.get('title', '')}\nContent: {r.get('content', '')}\nURL: {r.get('url', '')}"
            for r in search_results.get('results', [])
        ])
        prompt = (
            f"Given the following web search results, list up to 3 direct competitors for {company_name} ({company_url if company_url else ''}), "
            f"with their official websites. Only return a JSON list of objects with 'name' and 'url'.\n\nWeb search results:\n{context}"
        )
        openai_response = self.call_openai(prompt)
        import json
        try:
            competitors = json.loads(openai_response)
        except Exception:
            competitors = []
        return competitors

    def find_company_socials(self, company_name, company_url=None):
        # Step 2: Use Tavily to get web context for social links
        query = f"Find the official Instagram, TikTok, Facebook, and LinkedIn pages for {company_name}"
        if company_url:
            query += f" ({company_url})"
        query += ". Return only the social media page URLs."
        search_results = self.tavily_client.search(query, max_results=5)
        print(f"Social links search result: {search_results}")
        context = "\n\n".join([
            f"Title: {r.get('title', '')}\nContent: {r.get('content', '')}\nURL: {r.get('url', '')}"
            for r in search_results.get('results', [])
        ])
        prompt = (
            f"Given the following web search results, extract the official Instagram, Facebook, TikTok, and LinkedIn PROFILE URLs for {company_name} ({company_url if company_url else ''}). "
            f"Return a JSON object with keys 'instagram', 'facebook', 'tiktok', and 'linkedin', each mapping to a list of URLs. "
            f"IMPORTANT: Only include the main profile URLs for each platform (e.g., https://instagram.com/username), do NOT include posts, reels, or other subpages.\n\nWeb search results:\n{context}"
        )
        openai_response = self.call_openai(prompt)
        import json
        try:
            socials = json.loads(openai_response)
        except Exception:
            socials = {"instagram": [], "facebook": [], "tiktok": [], "linkedin": []}
        return socials

# Example usage
if __name__ == "__main__":
    import json
    finder = SimilarCompanyFinder()
    competitors = finder.find_competitors(
        company_name="afriex",
        company_url="https:/afriex.com/",
        products_services="cross-border payments, remittance"
    )
    print("Found competitors:")
    final_results = {}
    for comp in competitors:
        print(f"- {comp['name']} ({comp['url']})")
    print("\nSocial links for each competitor:")
    for comp in competitors:
        socials = finder.find_company_socials(comp['name'], comp['url'])
        print(f"{comp['name']}:")
        for platform, urls in socials.items():
            print(f"  {platform}: {urls}")
        final_results[comp['name']] = {
            'url': comp['url'],
            'socials': socials
        }
    # Print final results as JSON
    print("\nFinal JSON result:")
    print(json.dumps(final_results, indent=2))
    # Save to file
    with open('social_links_results.json', 'w') as f:
        json.dump(final_results, f, indent=2) 