from fastapi import FastAP<PERSON>, HTTPException, Depends
from pydantic import BaseModel, HttpUrl
from typing import List, Dict
from datetime import datetime
from .models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ob, GroupLink,ScrapedUser
from .database import get_db, init_db, test_db_connection
from .tasks import process_parent_job, process_group_member_scraping
import json
import logging
import dramatiq

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Social Media Scraping API")

@app.on_event("startup")
async def startup_event():
    """Initialize database on startup."""
    logger.info("Starting up Social Media Scraping API...")

    # Test database connection
    if not test_db_connection():
        logger.error("Failed to connect to database")
        raise Exception("Database connection failed")

    # Initialize database tables
    try:
        init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    db_status = test_db_connection()
    return {
        "status": "healthy" if db_status else "unhealthy",
        "database": "connected" if db_status else "disconnected"
    }

class ScrapingRequest(BaseModel):
    user_id: str  # New field for user identification
    business_domain: str
    business_goals: str
    target_market: str
    business_website_url: HttpUrl
    target_platforms: List[str]
    leads_per_platform: Dict[str, int]
    additional_criteria: str

class ScrapingResponse(BaseModel):
    success: bool
    parent_job_id: str
    child_job_ids: Dict[str, str]
    estimated_completion_time: str
    status: str
    created_at: datetime

@app.post("/api/scraping/create-job", response_model=ScrapingResponse)
async def create_scraping_job(request: ScrapingRequest, db = Depends(get_db)):
    """Create a new scraping job with parent and child jobs."""
    try:
        # Validate target platforms
        valid_platforms = ['facebook', 'instagram', 'linkedin', 'tiktok', 'telegram', 'whatsapp']
        if not all(platform in valid_platforms for platform in request.target_platforms):
            raise HTTPException(
                status_code=400,
                detail="Invalid platform specified. Valid platforms are: " + ", ".join(valid_platforms)
            )

        # Calculate total leads requested
        total_leads = sum(request.leads_per_platform.values())

        # Create parent job
        parent_job = ParentJob(
            user_id=request.user_id,
            business_domain=request.business_domain,
            business_goals=request.business_goals,
            target_market=request.target_market,
            business_website_url=str(request.business_website_url),
            additional_criteria=request.additional_criteria,
            status='scheduled',
            estimated_completion_time='4-6 hours',
            total_leads_requested=total_leads
        )
        db.add(parent_job)
        db.commit()
        db.refresh(parent_job)

        # Create child jobs
        child_job_ids = {}
        for platform in request.target_platforms:
            # Build scraping criteria (no competitor URLs)
            scraping_criteria = {
                'business_domain': request.business_domain,
                'business_goals': request.business_goals,
                'target_market': request.target_market,
                'website_url': str(request.business_website_url),
                'additional_criteria': request.additional_criteria
            }
            child_job = ChildJob(
                parent_job_id=parent_job.job_id,
                platform=platform,
                leads_requested=request.leads_per_platform.get(platform, 0),
                scraping_criteria=json.dumps(scraping_criteria)
            )
            db.add(child_job)
            db.commit()
            db.refresh(child_job)
            child_job_ids[platform] = child_job.job_id

        # Start processing the parent job
        process_parent_job.send(parent_job.job_id)

        return ScrapingResponse(
            success=True,
            parent_job_id=parent_job.job_id,
            child_job_ids=child_job_ids,
            estimated_completion_time='4-6 hours',
            status='scheduled',
            created_at=parent_job.created_at
        )

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/scraping/job/{job_id}")
async def get_job_status(job_id: str, db = Depends(get_db)):
    """Get the status of a scraping job."""
    try:
        # Check if it's a parent job
        parent_job = db.query(ParentJob).filter(ParentJob.job_id == job_id).first()
        if parent_job:
            child_jobs = db.query(ChildJob).filter(ChildJob.parent_job_id == job_id).all()
            return {
                "job_id": parent_job.job_id,
                "type": "parent",
                "status": parent_job.status,
                "total_leads_requested": parent_job.total_leads_requested,
                "total_leads_scraped": parent_job.total_leads_scraped,
                "child_jobs": [
                    {
                        "job_id": child.job_id,
                        "platform": child.platform,
                        "status": child.status,
                        "leads_requested": child.leads_requested,
                        "leads_scraped": child.leads_scraped
                    }
                    for child in child_jobs
                ]
            }

        # Check if it's a child job
        child_job = db.query(ChildJob).filter(ChildJob.job_id == job_id).first()
        if child_job:
            return {
                "job_id": child_job.job_id,
                "type": "child",
                "platform": child_job.platform,
                "status": child_job.status,
                "leads_requested": child_job.leads_requested,
                "leads_scraped": child_job.leads_scraped,
                "parent_job_id": child_job.parent_job_id
            }

        raise HTTPException(status_code=404, detail="Job not found")

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

class ManualActionRequest(BaseModel):
    parent_job_id: str
    platform: str  # 'telegram' or 'whatsapp'

class ManualActionResponse(BaseModel):
    success: bool
    message: str
    parent_job_id: str
    platform: str
    groups_found: int
    child_job_ids: List[str]

@app.post("/api/scraping/manual-action-completed", response_model=ManualActionResponse)
async def complete_manual_action(request: ManualActionRequest, db = Depends(get_db)):
    """Mark manual group joining as completed and trigger member scraping for all groups."""
    try:
        # Validate platform
        if request.platform not in ['telegram', 'whatsapp']:
            raise HTTPException(
                status_code=400,
                detail="Invalid platform. Must be 'telegram' or 'whatsapp'"
            )

        # Find the parent job
        parent_job = db.query(ParentJob).filter(ParentJob.job_id == request.parent_job_id).first()
        if not parent_job:
            raise HTTPException(
                status_code=404,
                detail=f"Parent job {request.parent_job_id} not found"
            )

        # Find child jobs for this platform that are awaiting manual action
        child_jobs = db.query(ChildJob).filter(
            ChildJob.parent_job_id == request.parent_job_id,
            ChildJob.platform == request.platform,
            ChildJob.status == 'awaiting_manual_action'
        ).all()

        if not child_jobs:
            raise HTTPException(
                status_code=404,
                detail=f"No jobs found awaiting manual action for {request.platform} in parent job {request.parent_job_id}"
            )

        child_job_ids = []
        total_groups = 0

        for child_job in child_jobs:
            # Get all group links for this child job
            group_links = db.query(GroupLink).filter(
                GroupLink.child_job_id == child_job.job_id,
                GroupLink.platform == request.platform
            ).all()

            if group_links:
                # Mark all groups as joined (assuming user has joined them)
                for group_link in group_links:
                    group_link.join_status = 'joined'
                
                db.commit()
                total_groups += len(group_links)
                child_job_ids.append(child_job.job_id)

                # Import the task function
                from .tasks import process_group_member_scraping
                
                # Trigger the member scraping task with retry mechanism
                process_group_member_scraping.send_with_options(
                    child_job.job_id,
                    retry_policy=dramatiq.retry.ExponentialBackoff(
                        max_retries=3,
                        min_backoff=1000,
                        max_backoff=10000
                    )
                )

        if total_groups == 0:
            raise HTTPException(
                status_code=400,
                detail=f"No groups found for {request.platform} in parent job {request.parent_job_id}"
            )

        return ManualActionResponse(
            success=True,
            message=f"Manual action completed. Started scraping {total_groups} groups for {request.platform} with retry mechanism.",
            parent_job_id=request.parent_job_id,
            platform=request.platform,
            groups_found=total_groups,
            child_job_ids=child_job_ids
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing manual action: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/scraping/pending-groups/{parent_job_id}/{platform}")
async def get_pending_groups_by_parent(parent_job_id: str, platform: str, db = Depends(get_db)):
    """Get list of groups that need manual joining for a specific parent job and platform."""
    try:
        if platform not in ['telegram', 'whatsapp']:
            raise HTTPException(
                status_code=400,
                detail="Invalid platform. Must be 'telegram' or 'whatsapp'"
            )

        # Verify parent job exists
        parent_job = db.query(ParentJob).filter(ParentJob.job_id == parent_job_id).first()
        if not parent_job:
            raise HTTPException(
                status_code=404,
                detail=f"Parent job {parent_job_id} not found"
            )

        # Find child jobs awaiting manual action
        child_jobs = db.query(ChildJob).filter(
            ChildJob.parent_job_id == parent_job_id,
            ChildJob.platform == platform,
            ChildJob.status == 'awaiting_manual_action'
        ).all()

        if not child_jobs:
            return {
                "parent_job_id": parent_job_id,
                "platform": platform,
                "pending_groups": [],
                "message": f"No pending groups found for {platform} in parent job {parent_job_id}"
            }

        # Get all group links for these jobs
        all_groups = []
        for child_job in child_jobs:
            group_links = db.query(GroupLink).filter(
                GroupLink.child_job_id == child_job.job_id,
                GroupLink.platform == platform
            ).all()
            
            for group_link in group_links:
                all_groups.append({
                    "child_job_id": child_job.job_id,
                    "group_name": group_link.group_name,
                    "group_link": group_link.group_link,
                    "member_count": group_link.member_count,
                    "join_status": group_link.join_status
                })

        return {
            "parent_job_id": parent_job_id,
            "platform": platform,
            "pending_groups": all_groups,
            "total_groups": len(all_groups)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting pending groups: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

class ScrapedUserResponse(BaseModel):
    user_id: str
    username: str
    display_name: str
    bio: str
    profile_url: str
    profile_image_url: str
    location: str
    verified: bool
    follower_count: int
    following_count: int
    additional_data: dict
    scraped_at: datetime

class PlatformUsersResponse(BaseModel):
    platform: str
    total_users: int
    users: List[ScrapedUserResponse]

class AllScrapedUsersResponse(BaseModel):
    parent_job_id: str
    total_users: int
    platforms: List[PlatformUsersResponse]
    summary: dict

@app.get("/api/scraping/users/{parent_job_id}", response_model=AllScrapedUsersResponse)
async def get_all_scraped_users(parent_job_id: str, db = Depends(get_db)):
    """Get all scraped users for a parent job, organized by platform."""
    try:
        # Verify parent job exists
        parent_job = db.query(ParentJob).filter(ParentJob.job_id == parent_job_id).first()
        if not parent_job:
            raise HTTPException(
                status_code=404,
                detail=f"Parent job {parent_job_id} not found"
            )

        # Get all child jobs for this parent
        child_jobs = db.query(ChildJob).filter(ChildJob.parent_job_id == parent_job_id).all()
        
        if not child_jobs:
            return AllScrapedUsersResponse(
                parent_job_id=parent_job_id,
                total_users=0,
                platforms=[],
                summary={}
            )

        # Group child jobs by platform
        platform_jobs = {}
        for child_job in child_jobs:
            if child_job.platform not in platform_jobs:
                platform_jobs[child_job.platform] = []
            platform_jobs[child_job.platform].append(child_job)

        platforms_data = []
        total_users = 0
        summary = {}

        for platform, jobs in platform_jobs.items():
            platform_users = []
            platform_total = 0
            
            for child_job in jobs:
                # Get all scraped users for this child job
                scraped_users = db.query(ScrapedUser).filter(
                    ScrapedUser.child_job_id == child_job.job_id
                ).all()
                
                for user in scraped_users:
                    platform_users.append(ScrapedUserResponse(
                        user_id=user.user_id or "",
                        username=user.username or "",
                        display_name=user.display_name or "",
                        bio=user.bio or "",
                        profile_url=user.profile_url or "",
                        profile_image_url=user.profile_image_url or "",
                        location=user.location or "",
                        verified=user.verified or False,
                        follower_count=user.follower_count or 0,
                        following_count=user.following_count or 0,
                        additional_data=user.additional_data or {},
                        scraped_at=user.scraped_at
                    ))
                    platform_total += 1
            
            total_users += platform_total
            
            platforms_data.append(PlatformUsersResponse(
                platform=platform,
                total_users=platform_total,
                users=platform_users
            ))
            
            # Add platform summary
            summary[platform] = {
                "total_users": platform_total,
                "completed_jobs": len([j for j in jobs if j.status == 'completed']),
                "failed_jobs": len([j for j in jobs if j.status == 'failed']),
                "pending_jobs": len([j for j in jobs if j.status in ['scheduled', 'in_progress', 'awaiting_manual_action', 'scraping_members']])
            }

        # Add overall summary
        summary["overall"] = {
            "total_users": total_users,
            "total_platforms": len(platforms_data),
            "parent_job_status": parent_job.status,
            "parent_job_created": parent_job.created_at.isoformat() if parent_job.created_at else None,
            "parent_job_completed": parent_job.actual_completion_time.isoformat() if parent_job.actual_completion_time else None
        }

        return AllScrapedUsersResponse(
            parent_job_id=parent_job_id,
            total_users=total_users,
            platforms=platforms_data,
            summary=summary
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting scraped users: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/scraping/users/{parent_job_id}/{platform}")
async def get_scraped_users_by_platform(parent_job_id: str, platform: str, db = Depends(get_db)):
    """Get scraped users for a specific platform within a parent job."""
    try:
        # Verify parent job exists
        parent_job = db.query(ParentJob).filter(ParentJob.job_id == parent_job_id).first()
        if not parent_job:
            raise HTTPException(
                status_code=404,
                detail=f"Parent job {parent_job_id} not found"
            )

        # Get child jobs for this platform
        child_jobs = db.query(ChildJob).filter(
            ChildJob.parent_job_id == parent_job_id,
            ChildJob.platform == platform
        ).all()
        
        if not child_jobs:
            return {
                "parent_job_id": parent_job_id,
                "platform": platform,
                "total_users": 0,
                "users": [],
                "message": f"No jobs found for platform {platform} in parent job {parent_job_id}"
            }

        platform_users = []
        total_users = 0
        
        for child_job in child_jobs:
            # Get all scraped users for this child job
            scraped_users = db.query(ScrapedUser).filter(
                ScrapedUser.child_job_id == child_job.job_id
            ).all()
            
            for user in scraped_users:
                platform_users.append({
                    "user_id": user.user_id or "",
                    "username": user.username or "",
                    "display_name": user.display_name or "",
                    "bio": user.bio or "",
                    "profile_url": user.profile_url or "",
                    "profile_image_url": user.profile_image_url or "",
                    "location": user.location or "",
                    "verified": user.verified or False,
                    "follower_count": user.follower_count or 0,
                    "following_count": user.following_count or 0,
                    "additional_data": user.additional_data or {},
                    "scraped_at": user.scraped_at.isoformat() if user.scraped_at else None,
                    "child_job_id": child_job.job_id
                })
                total_users += 1

        return {
            "parent_job_id": parent_job_id,
            "platform": platform,
            "total_users": total_users,
            "users": platform_users,
            "child_jobs": [
                {
                    "job_id": job.job_id,
                    "status": job.status,
                    "leads_requested": job.leads_requested,
                    "leads_scraped": job.leads_scraped,
                    "started_at": job.started_at.isoformat() if job.started_at else None,
                    "completed_at": job.completed_at.isoformat() if job.completed_at else None
                }
                for job in child_jobs
            ]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting scraped users by platform: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))