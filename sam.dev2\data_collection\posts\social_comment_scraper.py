from .instagram import InstagramCollector
from .tiktok import <PERSON><PERSON><PERSON>okCollector
from .facebook import FacebookCollector
from .linkedin import LinkedInCollector
from .comment_scraper import CommentScraper, SocialMediaPlatform, format_comments_for_output
import os
import json
import logging

from config.settings import Config



API_TOKEN = Config.api_keys.APIFY_API_TOKEN

def extract_unique_profiles(platform, platform_comments: dict) -> dict:
    """
    Extract unique user profiles from formatted comments for a platform.
    Ensures uniqueness by username only.
    Adds a 'profile_url' field for Instagram and TikTok.
    """
    profiles = {}
    for url, comments in platform_comments.items():
        for comment in comments:
            author = comment.get("author", {})
            name = author.get("name")
            if not name:
                continue
            if name not in profiles:
                profile = dict(author)  # copy all fields
                # Add profile_url for Instagram and TikTok
                if platform.lower() == "instagram":
                    profile["profile_url"] = f"https://instagram.com/{name}"
                elif platform.lower() == "tiktok":
                    profile["profile_url"] = f"https://www.tiktok.com/@{name}"
                profiles[name] = profile
    return profiles

def collect_and_scrape(platform, collector, profile_url, post_count=5):
    post_urls = collector.get_company_posts(profile_url, count=post_count)
    if not post_urls:
        print(f"No posts found for {platform.value} at {profile_url}")
        return {}
    scraper = CommentScraper(API_TOKEN)
    comments = scraper.scrape_comments(platform, post_urls)
    formatted_comments = format_comments_for_output(comments)
    print(scraper.generate_report())
    return formatted_comments

if __name__ == "__main__":
    urls = {
        SocialMediaPlatform.INSTAGRAM: "https://www.instagram.com/dippyshotit/",
        SocialMediaPlatform.FACEBOOK: "https://www.facebook.com/4",
        SocialMediaPlatform.LINKEDIN: "https://www.linkedin.com/company/microsoft/",
        SocialMediaPlatform.TIKTOK: "https://www.tiktok.com/@taylorswift"
    }
    collectors = {
        SocialMediaPlatform.INSTAGRAM: InstagramCollector(),
        SocialMediaPlatform.FACEBOOK: FacebookCollector(),
        SocialMediaPlatform.LINKEDIN: LinkedInCollector(),
        SocialMediaPlatform.TIKTOK: TikTokCollector()
    }
    all_results = {}
    for platform, collector in collectors.items():
        print(f"\n=== {platform.value.upper()} ===")
        formatted_comments = collect_and_scrape(platform, collector, urls[platform], post_count=3)
        all_results[platform.value] = formatted_comments

    # Save all results to a file
    with open('all_comments_results.json', 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)

    # Extract and save unique user profiles for each platform
    all_profiles = {}
    for platform, platform_comments in all_results.items():
        all_profiles[platform] = list(extract_unique_profiles(platform, platform_comments).values())

    with open('unique_user_profiles.json', 'w', encoding='utf-8') as f:
        json.dump(all_profiles, f, indent=2, ensure_ascii=False)

    print("Saved unique user profiles to unique_user_profiles.json")