import os
import sys
from typing import List, Dict
from dotenv import load_dotenv
import requests
from rich import print

from .base_collector import BaseSocialMediaCollector
from config.settings import Config


class FacebookCollector(BaseSocialMediaCollector):
    def __init__(self):
        self.api_key = Config.api_keys.FACEBOOK_RAPID_API_KEY
        self.api_host = "facebook-scraper3.p.rapidapi.com"
        self.headers = {
            "x-rapidapi-key": self.api_key,
            "x-rapidapi-host": self.api_host
        }

    def extract_id_from_url(self, url: str, is_group: bool = False) -> str:
        """Extract page ID or group ID from a Facebook URL"""
        if "facebook.com" not in url:
            return url

        # Handle URLs with or without protocol
        if "://" in url:
            clean_url = url.split("://")[1]
        else:
            clean_url = url

        # Split by slashes and remove empty parts
        parts = [p for p in clean_url.split('/') if p]

        if is_group and 'groups' in parts:
            # Find the part after "groups"
            groups_idx = parts.index('groups')
            if groups_idx + 1 < len(parts):
                group_id = parts[groups_idx + 1]
                # Remove query parameters
                if '?' in group_id:
                    group_id = group_id.split('?')[0]
                return group_id
        elif not is_group and ('facebook.com' in parts or 'www.facebook.com' in parts):
            # Find the index of facebook.com part
            fb_idx = -1
            if 'facebook.com' in parts:
                fb_idx = parts.index('facebook.com')
            elif 'www.facebook.com' in parts:
                fb_idx = parts.index('www.facebook.com')

            # Get the part after facebook.com if it exists
            if fb_idx >= 0 and fb_idx + 1 < len(parts):
                page_id = parts[fb_idx + 1]
                # Remove query parameters
                if '?' in page_id:
                    page_id = page_id.split('?')[0]
                return page_id

        return url

    def extract_post_id_from_url(self, post_url: str) -> str:
        """Extract post ID from a Facebook post URL"""
        # Handles URLs like .../posts/{post_id} or .../permalink.php?story_fbid={post_id}
        if '/posts/' in post_url:
            return post_url.split('/posts/')[-1].split('/')[0].split('?')[0]
        elif 'story_fbid=' in post_url:
            # permalink.php?story_fbid={post_id}&id={page_id}
            for part in post_url.split('?')[-1].split('&'):
                if part.startswith('story_fbid='):
                    return part.split('=')[1]
        elif '/groups/' in post_url and '/posts/' in post_url:
            # .../groups/{group_id}/posts/{post_id}
            return post_url.split('/posts/')[-1].split('/')[0].split('?')[0]
        return ""

    def get_company_posts(self, url: str, count: int = 20) -> List[str]:
        page_id = self.extract_id_from_url(url)
        print(f"Fetching posts for Facebook page: {page_id}")
        api_url = f"https://{self.api_host}/profile/posts"
        params = {"profile_id": page_id}
        try:
            resp = requests.get(api_url, headers=self.headers, params=params, timeout=15)
            if resp.status_code == 200:
                data = resp.json()
                posts = data.get("results", [])
                post_urls = []
                for post in posts[:count]:  # Use count to limit results
                    post_url = post.get("url")
                    if post_url:
                        post_urls.append(post_url)
                return post_urls
            else:
                print(f"[red]Failed to fetch posts: {resp.status_code}[/red]")
                return []
        except Exception as e:
            print(f"[red]Error fetching company posts: {e}[/red]")
            return []

    def get_profile_posts(self, url: str) -> List[str]:
        """Get all post URLs from a Facebook user profile"""
        return self.get_company_posts(url)

    def get_group_posts(self, url: str) -> List[str]:
        """Get all post URLs from a Facebook group"""
        group_id = self.extract_id_from_url(url, is_group=True)
        print(f"Fetching posts for Facebook group: {group_id}")
        api_url = f"https://{self.api_host}/group/posts"
        params = {"group_id": group_id, "sorting_order": "CHRONOLOGICAL"}
        try:
            resp = requests.get(api_url, headers=self.headers, params=params, timeout=15)
            if resp.status_code == 200:
                data = resp.json()
                posts = data.get("posts", [])  # <-- Use 'posts' for groups
                post_urls = []
                for post in posts:
                    post_url = post.get("url")
                    if post_url:
                        post_urls.append(post_url)
                return post_urls
            else:
                print(f"[red]Failed to fetch group posts: {resp.status_code}[/red]")
                return []
        except Exception as e:
            print(f"[red]Error fetching group posts: {e}[/red]")
            return []

    def get_post_details(self, post_url: str) -> Dict:
        """Get details for a Facebook post"""
        post_id = self.extract_post_id_from_url(post_url)
        if not post_id:
            print(f"[red]Could not extract post_id from URL: {post_url}[/red]")
            return {}
        api_url = f"https://{self.api_host}/post"
        params = {"post_id": post_id}
        try:
            resp = requests.get(api_url, headers=self.headers, params=params, timeout=15)
            if resp.status_code == 200:
                return resp.json()
            else:
                print(f"[red]Failed to fetch post details: {resp.status_code}[/red]")
                return {}
        except Exception as e:
            print(f"[red]Error fetching post details: {e}[/red]")
            return {}

if __name__ == "__main__":
    collector = FacebookCollector()

    # Example: Mark Zuckerberg's public profile (numeric ID: 4)
    profile_url = "https://www.facebook.com/4"
    print(f"User posts for {profile_url}:")
    profile_posts = collector.get_profile_posts(profile_url)
    if profile_posts:
        for post_url in profile_posts:
            print(post_url)
    else:
        print("No user posts found or API endpoint not available.")

    print("\n" + "-"*50 + "\n")

    # Example: NASA's public page (numeric ID: 54971236771)
    page_url = "https://www.facebook.com/54971236771"
    print(f"Page posts for {page_url}:")
    page_posts = collector.get_profile_posts(page_url)
    if page_posts:
        for post_url in page_posts:
            print(post_url)
    else:
        print("No page posts found or API endpoint not available.")

    print("\n" + "-"*50 + "\n")

    # Example: Facebook group (replace with a public group ID if needed)
    group_url = "https://www.facebook.com/groups/1439220986320043"
    print(f"Group posts for {group_url}:")
    group_posts = collector.get_group_posts(group_url)
    if group_posts:
        for post_url in group_posts:
            print(post_url)
    else:
        print("No group posts found or API endpoint not available.")

    print("\n" + "-"*50 + "\n")

    # Example post details (from NASA page if available)
    if page_posts:
        test_post_url = page_posts[0]
        print(f"Getting details for Facebook post: {test_post_url}")
        details = collector.get_post_details(test_post_url)
        if details:
            print("Post details:")
            print(details)
        else:
            print("No details found or an error occurred.")