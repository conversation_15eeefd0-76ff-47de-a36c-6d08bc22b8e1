Social Media Scraping System - Complete Task Specification
Overview
We need to build a robust social media scraping system that can extract user data from multiple
platforms (Telegram, WhatsApp, Facebook, Instagram, TikTok, and LinkedIn) based on business
requirements. The system should handle the entire workflow from job creation to completion, with
proper status tracking and manual intervention steps where needed.
Architecture Overview
The system uses a parent-child job structure:
• Parent Job: Tracks the overall scraping campaign
• Child Jobs: Individual platform-specific scraping tasks (6 separate jobs, one per platform)
• All child jobs are linked to their parent job ID for unified tracking
System Components
1. API Endpoint for Job Creation
Endpoint: POST /api/scraping/create-job
Input Parameters:
{
 "business_domain": "Healthcare Technology",
 "business_goals": "Lead generation for B2B SaaS platform",
 "target_market": "Healthcare professionals and clinic owners",
 "business_website_url": "https://example.com",
 "target_platforms": ["facebook", "instagram", "linkedin", "tiktok", "telegram", "whatsapp"],
 "leads_per_platform": {
 "facebook": 500,
 "instagram": 300,
 "linkedin": 1000,
 "tiktok": 200,
 "telegram": 400,
 "whatsapp": 300
 },
 "additional_criteria": "Only professionals aged 25-45, focus on decision makers in healthcare"
}
Response:
{
 "success": true,
 "parent_job_id": "parent_job_12345",
 "child_job_ids": {
 "facebook": "fb_job_67890",
 "instagram": "ig_job_67891",
 "linkedin": "li_job_67892",
 "tiktok": "tk_job_67893",
 "telegram": "tg_job_67894",
 "whatsapp": "wa_job_67895"
 },
 "estimated_completion_time": "4-6 hours",
 "status": "scheduled",
 "created_at": "2025-06-08T10:30:00Z"
}
2. Background Processing System
Technology: Dramatiq for task queue management
Process Flow:
Step 1: Job Initialization
• Cron job checks for scheduled parent jobs every 5 minutes
• When found, updates parent job status to pending
• Simultaneously updates all child jobs to pending
• Begins processing all platform-specific jobs
Step 2: Platform-Specific Processing
For Direct API Platforms (Facebook, Instagram, TikTok, LinkedIn):
1. Child job status: scheduled → pending → in_progress
2. Execute scraping using platform APIs/web scraping
3. Store results in database
4. Update child job status to completed
For Group-Based Platforms (WhatsApp, Telegram):
1. Child job status: scheduled → pending → in_progress
2. Scrape available group links and public data
3. Update child job status to awaiting_manual_action
4. Update parent job status to awaiting_manual_action
5. Send email notification to admin with group details
6. Wait for admin to manually join groups
7. Admin updates child job status to manual_action_completed
8. Resume scraping group members
9. Update child job status to completed
Step 3: Job Completion
• Monitor all child jobs for completion
• When all child jobs are completed, update parent job to completed
• Send completion notification email
3. Email Notification System
Awaiting Action Email Template:
Subject: Manual Action Required - WhatsApp/Telegram Group Joining
Dear Admin,
Parent Job ID: parent_job_12345
Business Domain: Healthcare Technology
The following groups require manual joining:
TELEGRAM GROUPS:
- Group Name: Healthcare Professionals Network
 Group Link: https://t.me/healthcarepros
 Job ID: tg_job_67894

WHATSAPP GROUPS:
- Group Name: Medical Tech Leaders
 Group Link: https://chat.whatsapp.com/xyz123
 Job ID: wa_job_67895
Please join these groups and then update the job status to "manual_action_completed" in the admin
panel.
Current Status: Awaiting Manual Action
Next Steps: Join groups → Update status → Automatic member scraping will resume
Database Models
Required Tables
-- Parent Jobs Table
CREATE TABLE parent_jobs (
 id SERIAL PRIMARY KEY,
 job_id VARCHAR(50) UNIQUE NOT NULL,
 business_domain VARCHAR(255),
 business_goals TEXT,
 target_market TEXT,
 business_website_url VARCHAR(500),
 additional_criteria TEXT,
 status VARCHAR(50) DEFAULT 'scheduled', -- scheduled, pending, in_progress,
awaiting_manual_action, completed, failed
 estimated_completion_time VARCHAR(50),
 actual_completion_time TIMESTAMP,
 total_leads_requested INTEGER,
 total_leads_scraped INTEGER DEFAULT 0,
 created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
 updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
-- Child Jobs Table (Platform-specific jobs)
CREATE TABLE child_jobs (
 id SERIAL PRIMARY KEY,
 job_id VARCHAR(50) UNIQUE NOT NULL,
 parent_job_id VARCHAR(50) REFERENCES parent_jobs(job_id),
 platform VARCHAR(20) NOT NULL, -- facebook, instagram, linkedin, tiktok, telegram, whatsapp
 status VARCHAR(50) DEFAULT 'scheduled', -- scheduled, pending, in_progress,
awaiting_manual_action, manual_action_completed, completed, failed
 leads_requested INTEGER,
 leads_scraped INTEGER DEFAULT 0,
 scraping_criteria JSONB, -- Platform-specific search criteria
 error_message TEXT,
 started_at TIMESTAMP,
 completed_at TIMESTAMP,
 created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
 updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
-- Scraped Users Table
CREATE TABLE scraped_users (
 id SERIAL PRIMARY KEY,
 child_job_id VARCHAR(50) REFERENCES child_jobs(job_id),
 platform VARCHAR(20) NOT NULL,
 user_id VARCHAR(100), -- Platform-specific user ID
 username VARCHAR(100),
 display_name VARCHAR(255),
 bio TEXT,
 follower_count INTEGER,
 following_count INTEGER,
 profile_url VARCHAR(500),
 profile_image_url VARCHAR(500),
 location VARCHAR(255),
 verified BOOLEAN DEFAULT FALSE,
 additional_data JSONB, -- Platform-specific additional fields
 scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
-- Group Links Table (for Telegram/WhatsApp)
CREATE TABLE group_links (
 id SERIAL PRIMARY KEY,
 child_job_id VARCHAR(50) REFERENCES child_jobs(job_id),
 platform VARCHAR(20) NOT NULL,
 group_name VARCHAR(255),
 group_link VARCHAR(500),
 group_id VARCHAR(100),
 member_count INTEGER,
 join_status VARCHAR(50) DEFAULT 'pending', -- pending, joined, failed
 manual_join_required BOOLEAN DEFAULT TRUE,
 scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
-- Job Logs Table
CREATE TABLE job_logs (
 id SERIAL PRIMARY KEY,
 job_id VARCHAR(50), -- Can reference either parent or child job
 job_type VARCHAR(20), -- parent, child
 log_level VARCHAR(20), -- info, warning, error
 message TEXT,
 additional_data JSONB,
 created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
-- Admin Actions Table
CREATE TABLE admin_actions (
 id SERIAL PRIMARY KEY,
 job_id VARCHAR(50),
 action_type VARCHAR(50), -- manual_join_completed, status_update, etc.
 description TEXT,
 performed_by VARCHAR(100),
 performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
Index Recommendations
-- Performance indexes
CREATE INDEX idx_parent_jobs_status ON parent_jobs(status);
CREATE INDEX idx_child_jobs_parent_id ON child_jobs(parent_job_id);
CREATE INDEX idx_child_jobs_status ON child_jobs(status);
CREATE INDEX idx_scraped_users_job_id ON scraped_users(child_job_id);
CREATE INDEX idx_group_links_job_id ON group_links(child_job_id);
CREATE INDEX idx_job_logs_job_id ON job_logs(job_id);
Implementation Steps
Phase 1: Database Setup
1. Set up local PostgreSQL instance
2. Create all database tables using the provided SQL
3. Set up connection pooling and ORM (SQLAlchemy recommended)
Phase 2: API Development
1. Create the job creation endpoint
2. Implement input validation and sanitization
3. Add job ID generation logic
4. Implement parent-child job creation logic
Phase 3: Background Processing
1. Set up Dramatiq workers
2. Implement cron job for checking scheduled jobs
3. Create platform-specific scraping modules
4. Implement status update mechanisms
Phase 4: Manual Action Workflow
1. Create email notification system
2. Build admin interface for status updates
3. Implement group joining workflow for Telegram/WhatsApp
Phase 5: Monitoring & Logging
1. Add comprehensive logging
2. Create job monitoring dashboard
3. Implement error handling and retry mechanisms
Key Features
Status Tracking
• Real-time status updates for both parent and child jobs
• Detailed logging of all scraping activities
• Progress tracking with leads requested vs scraped
Error Handling
• Retry mechanisms for failed scraping attempts
• Graceful degradation when individual platforms fail
• Detailed error logging for debugging
Scalability
• Independent platform processing allows for parallel execution
• Modular design makes it easy to add new platforms
• Database optimization for handling large volumes of scraped data
Security
• Input validation to prevent injection attacks
• Rate limiting to avoid platform bans
• Secure credential management for platform APIs
This architecture provides a robust, scalable solution for social media scraping with proper job
tracking, manual intervention capabilities, and comprehensive data management.





ssh -i "/Users/<USER>/Downloads/jordan.pem" <EMAIL>
