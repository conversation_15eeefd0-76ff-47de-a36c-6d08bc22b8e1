# =============================================================================
# Lead Generation Bot - Environment Configuration
# =============================================================================
# Copy this file to .env and update the values with your actual credentials
# cp env.example .env
#
# This file matches the configuration structure in config/settings.py
# All variables are organized according to their respective config classes

# =============================================================================
# Database Configuration (DatabaseConfig class)
# =============================================================================

# PostgreSQL Database Configuration
DATABASE_URL=postgresql://postgres:1234@localhost:5432/delegen_ai
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=1234
DB_NAME=delegen_ai

# AWS RDS Configuration (optional - for production)
AWS_DB_HOST=
AWS_DB_NAME=
AWS_DB_USER=
AWS_DB_PASSWORD=

# =============================================================================
# Redis Configuration (RedisConfig class)
# =============================================================================

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# =============================================================================
# Email Configuration (EmailConfig class)
# =============================================================================

SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# =============================================================================
# API Keys Configuration (APIKeysConfig class)
# =============================================================================

# Social Media API Keys
INSTAGRAM_RAPID_API_KEY=your_instagram_api_key_here
LINKEDIN_RAPID_API_KEY=your_linkedin_api_key_here
TIKTOK_RAPID_API_KEY=your_tiktok_api_key_here
FACEBOOK_RAPID_API_KEY=your_facebook_api_key_here

# TikTok API Configuration
TIKTOK_RAPID_API_HOST=tiktok-api23.p.rapidapi.com

# Apify Configuration
APIFY_API_TOKEN=**********************************************
APIFY_ACTOR_ID_INSTAGRAM=SbK00X0JYCPblD2wp
APIFY_ACTOR_ID_FACEBOOK=us5srxAYnsrkgUv2v
APIFY_ACTOR_ID_LINKEDIN=8xICdc1375BbZdgE3
APIFY_ACTOR_ID_TIKTOK=BDec00yAmCm1QbMEI

# Telegram API Keys
# Get these from https://my.telegram.org/apps
TELEGRAM_API_ID=27928447
TELEGRAM_API_HASH=f9ab963bd50e9669e6ea7d7da4da1e0f
TELEGRAM_PHONE=+2349017491405

# WhatsApp API Keys
# Get this from https://gate.whapi.cloud/
WHATSAPP_TOKEN=IvvTzJXb9hPkunTFBOhptCi8WRDceQnU

# AI/ML API Keys
OPENAI_API_KEY_VALID=your_openai_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here
GROQ_API_KEY=your_groq_api_key_here

# =============================================================================
# Application Configuration (AppConfig class)
# =============================================================================

# Application Settings
DEBUG=False
API_HOST=0.0.0.0
API_PORT=8080

# Dramatiq Worker Settings
DRAMATIQ_PROCESSES=1
DRAMATIQ_THREADS=1

# Logging Settings
LOG_LEVEL=INFO
LOG_FILE=app.log

# =============================================================================
# Configuration Notes
# =============================================================================

# Required Variables (for basic functionality):
# - DATABASE_URL: PostgreSQL connection string
# - REDIS_HOST: Redis server hostname
# - OPENAI_API_KEY_VALID: OpenAI API key for AI features

# Optional Variables (for enhanced functionality):
# - All social media API keys for platform-specific scraping
# - Email configuration for notifications
# - AWS RDS configuration for production deployment

# =============================================================================
# Setup Instructions
# =============================================================================

# 1. Database Setup:
#    - Install PostgreSQL
#    - Create database: CREATE DATABASE delegen_ai;
#    - Create test database: CREATE DATABASE delegen_ai_test;

# 2. Redis Setup:
#    - Install Redis: brew install redis (macOS) or apt-get install redis-server (Ubuntu)
#    - Start Redis: redis-server

# 3. API Keys Setup:
#    - Telegram: https://my.telegram.org/apps
#    - WhatsApp: https://gate.whapi.cloud/
#    - OpenAI: https://platform.openai.com/api-keys
#    - Tavily: https://tavily.com/
#    - Social Media APIs: Various providers

# 4. Email Setup:
#    - For Gmail, use App Passwords (not regular password)
#    - Enable 2FA on your Google account first

# 5. Environment Variables:
#    - Never commit .env file to version control
#    - Use env.example as a template
#    - Set different values for development, staging, and production

# 6. Security Best Practices:
#    - Use strong, unique passwords
#    - Rotate API keys regularly
#    - Use environment-specific configurations
#    - Monitor API usage and costs

# =============================================================================
# Configuration Validation
# =============================================================================

# The application will validate required settings on startup
# Missing required settings will be logged as errors
# Use Config.validate_required_settings() to check configuration
# Use Config.print_config_summary() to see current configuration status 